{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\litigation_approval.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\litigation_approval.vue", "mtime": 1754121746860}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0TGl0aWdhdGlvbkNvc3RBcHByb3ZhbCwNCiAgZ2V0TGl0aWdhdGlvbkNvc3RTdWJtaXNzaW9uUmVjb3JkcywNCiAgYXBwcm92ZUxpdGlnYXRpb25Db3N0UmVjb3JkLA0KICBiYXRjaEFwcHJvdmVMaXRpZ2F0aW9uQ29zdFJlY29yZHMsDQogIHN0YXJ0TGl0aWdhdGlvbkFwcHJvdmFsRmxvdywNCiAgYXBwcm92ZUxpdGlnYXRpb25Db3N0RmxvdywNCiAgcmVqZWN0TGl0aWdhdGlvbkNvc3RGbG93LA0KICBiYXRjaEFwcHJvdmVMaXRpZ2F0aW9uQ29zdEZsb3cNCn0gZnJvbSAiQC9hcGkvbGl0aWdhdGlvbl9jb3N0X2FwcHJvdmFsL2xpdGlnYXRpb25fY29zdF9hcHByb3ZhbCINCmltcG9ydCBhcmVhTGlzdCBmcm9tICIuLi8uLi8uLi9hc3NldHMvYXJlYS5qc29uIg0KaW1wb3J0IHVzZXJJbmZvIGZyb20gJ0AvbGF5b3V0L2NvbXBvbmVudHMvRGlhbG9nL3VzZXJJbmZvLnZ1ZScNCmltcG9ydCBBcHByb3ZhbE1hbmFnZXIgZnJvbSAiQC91dGlscy9hcHByb3ZhbFN0YXR1cyINCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlZtX2Nhcl9vcmRlciIsDQogIGNvbXBvbmVudHM6IHsNCiAgICB1c2VySW5mbywNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIFZJRVfooajmoLzmlbDmja4NCiAgICAgIHZtX2Nhcl9vcmRlckxpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIC8vIOS4peagvOaMieeFpznkuKrnrZvpgInmnaHku7YNCiAgICAgICAgLy8gMS4g6LS35qy+5Lq65aeT5ZCNDQogICAgICAgIGN1c3RvbWVyTmFtZTogbnVsbCwNCiAgICAgICAgLy8gMi4g6LS35qy+5Lq66Lqr5Lu96K+B5Y+3DQogICAgICAgIGNlcnRJZDogbnVsbCwNCiAgICAgICAgLy8gMy4g5Ye65Y2V5rig6YGTDQogICAgICAgIGpnTmFtZTogbnVsbCwNCiAgICAgICAgLy8gNC4g5pS+5qy+6ZO26KGMDQogICAgICAgIGxlbmRpbmdCYW5rOiBudWxsLA0KICAgICAgICAvLyA1LiDms5Xor4nnirbmgIEo5aSa57qnKQ0KICAgICAgICBsaXRpZ2F0aW9uU3RhdHVzOiBudWxsLA0KICAgICAgICAvLyA2LiDnlLPor7fkuroNCiAgICAgICAgYXBwbGljYXRpb25CeTogbnVsbCwNCiAgICAgICAgLy8gNy4g6LS555So57G75Z6LDQogICAgICAgIGNvc3RDYXRlZ29yeTogbnVsbCwNCiAgICAgICAgLy8gOC4g5a6h5om554q25oCBDQogICAgICAgIGFwcHJvdmFsU3RhdHVzOiBudWxsLA0KICAgICAgICAvLyA5LiDnlLPor7fml7bpl7TljLrpl7QNCiAgICAgICAgc3RhcnRUaW1lOiBudWxsLA0KICAgICAgICBlbmRUaW1lOiBudWxsLA0KICAgICAgICAvLyAxMC4g5a6h5om55pe26Ze05Yy66Ze0DQogICAgICAgIGFwcHJvdmFsU3RhcnRUaW1lOiBudWxsLA0KICAgICAgICBhcHByb3ZhbEVuZFRpbWU6IG51bGwsDQogICAgICB9LA0KICAgICAgLy8g5pel5pyf6IyD5Zu0DQogICAgICBkYXRlUmFuZ2U6IFtdLA0KICAgICAgLy8g5a6h5om55pel5pyf6IyD5Zu0DQogICAgICBhcHByb3ZhbERhdGVSYW5nZTogW10sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHsNCiAgICAgICAgaWQ6JycsDQogICAgICAgIHN0YXR1czogMCwNCiAgICAgICAgcmVqZWN0UmVhc29uOm51bGwsDQogICAgICB9LA0KICAgICAgLy8g5b2T5YmN5a6h5om56K6w5b2VDQogICAgICBjdXJyZW50UmVjb3JkOiB7fSwNCiAgICAgIC8vIOi0ueeUqOaPkOS6pOiusOW9leWIl+ihqA0KICAgICAgc3VibWlzc2lvblJlY29yZHM6IFtdLA0KICAgICAgLy8g6K6w5b2V5Yqg6L2954q25oCBDQogICAgICByZWNvcmRzTG9hZGluZzogZmFsc2UsDQogICAgICAvLyDpgInkuK3nmoTorrDlvZUNCiAgICAgIHNlbGVjdGVkUmVjb3JkczogW10sDQogICAgICAvLyDljZXkuKrlrqHmibnlr7nor53moYYNCiAgICAgIHNpbmdsZUFwcHJvdmFsT3BlbjogZmFsc2UsDQogICAgICBzaW5nbGVBcHByb3ZhbEZvcm06IHsNCiAgICAgICAgaWQ6ICcnLA0KICAgICAgICBzdGF0dXM6ICcwJywNCiAgICAgICAgcmVqZWN0UmVhc29uOiAnJw0KICAgICAgfSwNCiAgICAgIC8vIOWNleS4quWuoeaJueWvueivneahhg0KICAgICAgc2luZ2xlQXBwcm92YWxEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHNpbmdsZUFwcHJvdmFsRm9ybTogew0KICAgICAgICBpZDogJycsDQogICAgICAgIGFjdGlvbjogJycsIC8vICdhcHByb3ZlJyDmiJYgJ3JlamVjdCcNCiAgICAgICAgcmVqZWN0UmVhc29uOiAnJw0KICAgICAgfSwNCiAgICAgIC8vIOaJuemHj+WuoeaJueWvueivneahhg0KICAgICAgYmF0Y2hBcHByb3ZhbERpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgYmF0Y2hBcHByb3ZhbEZvcm06IHsNCiAgICAgICAgYWN0aW9uOiAnJywgLy8gJ2FwcHJvdmUnIOaIliAncmVqZWN0Jw0KICAgICAgICByZWplY3RSZWFzb246ICcnDQogICAgICB9LA0KICAgICAgLy8g5a6h5om55rWB56iL5a+56K+d5qGGDQogICAgICBmbG93QXBwcm92YWxPcGVuOiBmYWxzZSwNCiAgICAgIGZsb3dBcHByb3ZhbEZvcm06IHsNCiAgICAgICAgaWQ6ICcnLA0KICAgICAgICBhY3Rpb246ICdhcHByb3ZlJywgLy8gJ2FwcHJvdmUnIOaIliAncmVqZWN0Jw0KICAgICAgICBjdXJyZW50U3RhdHVzOiAwLA0KICAgICAgICByZWplY3RSZWFzb246ICcnDQogICAgICB9LA0KICAgICAgLy8g5om56YeP5a6h5om55rWB56iL5a+56K+d5qGGDQogICAgICBiYXRjaEZsb3dBcHByb3ZhbE9wZW46IGZhbHNlLA0KICAgICAgYmF0Y2hGbG93QXBwcm92YWxGb3JtOiB7DQogICAgICAgIGFjdGlvbjogJ2FwcHJvdmUnLCAvLyAnYXBwcm92ZScg5oiWICdyZWplY3QnDQogICAgICAgIHJlamVjdFJlYXNvbjogJycNCiAgICAgIH0sDQogICAgICAvLyDotLfmrL7kurrkv6Hmga/nm7jlhbMNCiAgICAgIHVzZXJJbmZvVmlzaWJsZTogZmFsc2UsDQogICAgICBjdXN0b21lckluZm86IHt9LA0KICAgICAgLy8g5rOV6K+J54q25oCB5aSa57qn6YCJ6aG5DQogICAgICBsaXRpZ2F0aW9uU3RhdHVzT3B0aW9uczogWw0KICAgICAgICB7DQogICAgICAgICAgdmFsdWU6ICfotbfor4knLA0KICAgICAgICAgIGxhYmVsOiAn6LW36K+JJywNCiAgICAgICAgICBjaGlsZHJlbjogWw0KICAgICAgICAgICAgeyB2YWx1ZTogJ+i1t+iviS3lh4blpIfmnZDmlpknLCBsYWJlbDogJ+WHhuWkh+adkOaWmScgfSwNCiAgICAgICAgICAgIHsgdmFsdWU6ICfotbfor4kt5bey5o+Q5LqkJywgbGFiZWw6ICflt7Lmj5DkuqQnIH0sDQogICAgICAgICAgICB7IHZhbHVlOiAn6LW36K+JLeazlemZouWPl+eQhicsIGxhYmVsOiAn5rOV6Zmi5Y+X55CGJyB9DQogICAgICAgICAgXQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdmFsdWU6ICflrqHnkIYnLA0KICAgICAgICAgIGxhYmVsOiAn5a6h55CGJywNCiAgICAgICAgICBjaGlsZHJlbjogWw0KICAgICAgICAgICAgeyB2YWx1ZTogJ+WuoeeQhi3lvIDluq3lrqHnkIYnLCBsYWJlbDogJ+W8gOW6reWuoeeQhicgfSwNCiAgICAgICAgICAgIHsgdmFsdWU6ICflrqHnkIYt562J5b6F5Yik5YazJywgbGFiZWw6ICfnrYnlvoXliKTlhrMnIH0sDQogICAgICAgICAgICB7IHZhbHVlOiAn5a6h55CGLeS4gOWuoeWIpOWGsycsIGxhYmVsOiAn5LiA5a6h5Yik5YazJyB9DQogICAgICAgICAgXQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdmFsdWU6ICfmiafooYwnLA0KICAgICAgICAgIGxhYmVsOiAn5omn6KGMJywNCiAgICAgICAgICBjaGlsZHJlbjogWw0KICAgICAgICAgICAgeyB2YWx1ZTogJ+aJp+ihjC3nlLPor7fmiafooYwnLCBsYWJlbDogJ+eUs+ivt+aJp+ihjCcgfSwNCiAgICAgICAgICAgIHsgdmFsdWU6ICfmiafooYwt5omn6KGM5LitJywgbGFiZWw6ICfmiafooYzkuK0nIH0sDQogICAgICAgICAgICB7IHZhbHVlOiAn5omn6KGMLeaJp+ihjOWujOavlScsIGxhYmVsOiAn5omn6KGM5a6M5q+VJyB9DQogICAgICAgICAgXQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdmFsdWU6ICfnu5PmoYgnLA0KICAgICAgICAgIGxhYmVsOiAn57uT5qGIJywNCiAgICAgICAgICBjaGlsZHJlbjogWw0KICAgICAgICAgICAgeyB2YWx1ZTogJ+e7k+ahiC3og5zor4nnu5PmoYgnLCBsYWJlbDogJ+iDnOiviee7k+ahiCcgfSwNCiAgICAgICAgICAgIHsgdmFsdWU6ICfnu5PmoYgt6LSl6K+J57uT5qGIJywgbGFiZWw6ICfotKXor4nnu5PmoYgnIH0sDQogICAgICAgICAgICB7IHZhbHVlOiAn57uT5qGILeWSjOino+e7k+ahiCcsIGxhYmVsOiAn5ZKM6Kej57uT5qGIJyB9DQogICAgICAgICAgXQ0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgICBrZXlQcm92aW5jZTonJywNCiAgICAgICAga2V5Q2l0eTonJywNCiAgICAgICAga2V5Qm9yb3VnaDonJywNCiAgICAgICAga2V5RGV0YWlsQWRkcmVzczonJywNCiAgICAgIH0sDQogICAgICBwcm92aW5jZUxpc3Q6YXJlYUxpc3QsDQogICAgICBjaXR5TGlzdDpbXSwNCiAgICAgIGRpc3RyaWN0TGlzdDpbXQ0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivouazleiviei0ueeUqOWuoeaJueWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICBsaXN0TGl0aWdhdGlvbkNvc3RBcHByb3ZhbCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy52bV9jYXJfb3JkZXJMaXN0ID0gcmVzcG9uc2Uucm93cw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZQ0KICAgICAgdGhpcy5yZXNldCgpDQogICAgfSwNCiAgICAvLyDooajljZXph43nva4NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgaWQ6JycsDQogICAgICAgIHN0YXR1czogMCwNCiAgICAgICAgcmVqZWN0UmVhc29uOm51bGwsDQogICAgICB9DQogICAgICB0aGlzLmN1cnJlbnRSZWNvcmQgPSB7fQ0KICAgICAgdGhpcy5zdWJtaXNzaW9uUmVjb3JkcyA9IFtdDQogICAgICB0aGlzLnNlbGVjdGVkUmVjb3JkcyA9IFtdDQogICAgICB0aGlzLnNpbmdsZUFwcHJvdmFsT3BlbiA9IGZhbHNlDQogICAgICB0aGlzLmJhdGNoQXBwcm92YWxPcGVuID0gZmFsc2UNCiAgICAgIHRoaXMuc2luZ2xlQXBwcm92YWxGb3JtID0gew0KICAgICAgICBpZDogJycsDQogICAgICAgIHN0YXR1czogJzAnLA0KICAgICAgICByZWplY3RSZWFzb246ICcnDQogICAgICB9DQogICAgICB0aGlzLnNpbmdsZUFwcHJvdmFsRGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgICB0aGlzLnNpbmdsZUFwcHJvdmFsRm9ybSA9IHsNCiAgICAgICAgaWQ6ICcnLA0KICAgICAgICBhY3Rpb246ICcnLA0KICAgICAgICByZWplY3RSZWFzb246ICcnDQogICAgICB9DQogICAgICB0aGlzLmJhdGNoQXBwcm92YWxEaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICAgIHRoaXMuYmF0Y2hBcHByb3ZhbEZvcm0gPSB7DQogICAgICAgIGFjdGlvbjogJycsDQogICAgICAgIHJlamVjdFJlYXNvbjogJycNCiAgICAgIH0NCiAgICAgIHRoaXMuZmxvd0FwcHJvdmFsT3BlbiA9IGZhbHNlDQogICAgICB0aGlzLmZsb3dBcHByb3ZhbEZvcm0gPSB7DQogICAgICAgIGlkOiAnJywNCiAgICAgICAgYWN0aW9uOiAnYXBwcm92ZScsDQogICAgICAgIGN1cnJlbnRTdGF0dXM6IDAsDQogICAgICAgIHJlamVjdFJlYXNvbjogJycNCiAgICAgIH0NCiAgICAgIHRoaXMuYmF0Y2hGbG93QXBwcm92YWxPcGVuID0gZmFsc2UNCiAgICAgIHRoaXMuYmF0Y2hGbG93QXBwcm92YWxGb3JtID0gew0KICAgICAgICBhY3Rpb246ICdhcHByb3ZlJywNCiAgICAgICAgcmVqZWN0UmVhc29uOiAnJw0KICAgICAgfQ0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKQ0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMQ0KICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5kYXRlUmFuZ2UgPSBbXQ0KICAgICAgdGhpcy5hcHByb3ZhbERhdGVSYW5nZSA9IFtdDQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIikNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pZCkNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCE9PTENCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aA0KICAgIH0sDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZQ0KICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqBWSUVXIg0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMuY3VycmVudFJlY29yZCA9IHJvdw0KICAgICAgdGhpcy5sb2FkU3VibWlzc2lvblJlY29yZHMocm93LmxpdGlnYXRpb25DYXNlSWQpDQogICAgICB0aGlzLm9wZW4gPSB0cnVlDQogICAgfSwNCg0KICAgIC8qKiDmibnph4/kv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVCYXRjaEVkaXQoKSB7DQogICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign6K+36YCJ5oup5Y2V5p2h6K6w5b2V6L+b6KGM5a6h5om55pON5L2cJykNCiAgICB9LA0KDQogICAgLyoqIOWKoOi9vei0ueeUqOaPkOS6pOiusOW9lSAqLw0KICAgIGxvYWRTdWJtaXNzaW9uUmVjb3JkcyhsaXRpZ2F0aW9uQ2FzZUlkKSB7DQogICAgICB0aGlzLnJlY29yZHNMb2FkaW5nID0gdHJ1ZQ0KICAgICAgZ2V0TGl0aWdhdGlvbkNvc3RTdWJtaXNzaW9uUmVjb3JkcyhsaXRpZ2F0aW9uQ2FzZUlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5zdWJtaXNzaW9uUmVjb3JkcyA9IHJlc3BvbnNlLmRhdGEgfHwgW10NCiAgICAgICAgdGhpcy5yZWNvcmRzTG9hZGluZyA9IGZhbHNlDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMucmVjb3Jkc0xvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLyoqIOiusOW9lemAieaLqeWPmOWMliAqLw0KICAgIGhhbmRsZVJlY29yZFNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuc2VsZWN0ZWRSZWNvcmRzID0gc2VsZWN0aW9uDQogICAgfSwNCg0KICAgIC8qKiDljZXkuKrlrqHmibkgKi8NCiAgICBoYW5kbGVTaW5nbGVBcHByb3ZlKHJlY29yZCwgc3RhdHVzKSB7DQogICAgICB0aGlzLnNpbmdsZUFwcHJvdmFsRm9ybS5pZCA9IHJlY29yZC5pZA0KICAgICAgdGhpcy5zaW5nbGVBcHByb3ZhbEZvcm0uc3RhdHVzID0gc3RhdHVzDQogICAgICB0aGlzLnNpbmdsZUFwcHJvdmFsRm9ybS5yZWplY3RSZWFzb24gPSAnJw0KICAgICAgdGhpcy5zaW5nbGVBcHByb3ZhbE9wZW4gPSB0cnVlDQogICAgfSwNCg0KICAgIC8qKiDnoa7orqTljZXkuKrlrqHmibkgKi8NCiAgICBjb25maXJtU2luZ2xlQXBwcm92YWwoKSB7DQogICAgICBpZiAodGhpcy5zaW5nbGVBcHByb3ZhbEZvcm0uc3RhdHVzID09ICcxJyAmJiAhdGhpcy5zaW5nbGVBcHByb3ZhbEZvcm0ucmVqZWN0UmVhc29uKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfor7fovpPlhaXmi5Lnu53ljp/lm6AnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgYXBwcm92ZUxpdGlnYXRpb25Db3N0UmVjb3JkKHRoaXMuc2luZ2xlQXBwcm92YWxGb3JtKS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygn5a6h5om55oiQ5YqfJykNCiAgICAgICAgdGhpcy5zaW5nbGVBcHByb3ZhbE9wZW4gPSBmYWxzZQ0KICAgICAgICB0aGlzLmxvYWRTdWJtaXNzaW9uUmVjb3Jkcyh0aGlzLmN1cnJlbnRSZWNvcmQubGl0aWdhdGlvbkNhc2VJZCkNCiAgICAgICAgdGhpcy5nZXRMaXN0KCkgLy8g5Yi35paw5Li75YiX6KGoDQogICAgICB9KQ0KICAgIH0sDQoNCg0KDQogICAgLyoqIOS4u+WIl+ihqOaJuemHj+WuoeaJuSAqLw0KICAgIGhhbmRsZUJhdGNoQXBwcm92ZU1haW4oc3RhdHVzKSB7DQogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfor7fpgInmi6nopoHlrqHmibnnmoTorrDlvZUnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgY29uc3Qgc3RhdHVzVGV4dCA9IHN0YXR1cyA9PT0gJzAnID8gJ+mAmui/hycgOiAn5ouS57udJw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybShg56Gu6K6k6KaB5om56YePJHtzdGF0dXNUZXh0femAieS4reeahCAke3RoaXMuaWRzLmxlbmd0aH0g5p2h6K6w5b2V5ZCX77yfYCkudGhlbigoKSA9PiB7DQogICAgICAgIGNvbnN0IGRhdGEgPSB7DQogICAgICAgICAgaWRzOiB0aGlzLmlkcywNCiAgICAgICAgICBzdGF0dXM6IHN0YXR1cywNCiAgICAgICAgICByZWplY3RSZWFzb246IHN0YXR1cyA9PT0gJzEnID8gJ+aJuemHj+aLkue7nScgOiAnJw0KICAgICAgICB9DQoNCiAgICAgICAgcmV0dXJuIGJhdGNoQXBwcm92ZUxpdGlnYXRpb25Db3N0UmVjb3JkcyhkYXRhKQ0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoYOaJuemHjyR7c3RhdHVzVGV4dH3miJDlip9gKQ0KICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pDQogICAgfSwNCg0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUoKSB7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTpgInkuK3nmoTmlbDmja7pobnvvJ8nKS50aGVuKCgpID0+IHsNCiAgICAgICAgLy8g6L+Z6YeM5Y+v5Lul6LCD55So5Yig6ZmkQVBJ77yM5pqC5pe25Y+q5piv5o+Q56S6DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOWKn+iDveaaguacquWunueOsCIpDQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSkNCiAgICB9LA0KDQoNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnbGl0aWdhdGlvbl9jb3N0X2FwcHJvdmFsL2xpdGlnYXRpb25fY29zdF9hcHByb3ZhbC9leHBvcnQnLCB7DQogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMNCiAgICAgIH0sIGBsaXRpZ2F0aW9uX2Nvc3RfYXBwcm92YWxfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfSwNCg0KICAgIC8qKiDmiZPlvIDotLfmrL7kurrkv6Hmga8gKi8NCiAgICBvcGVuVXNlckluZm8ocm93KSB7DQogICAgICBpZiAoIXJvdy5jdXN0b21lcklkICYmICFyb3cuYXBwbHlJZCkgew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign5peg5rOV6I635Y+W6LS35qy+5Lq65L+h5oGvJykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIHRoaXMuY3VzdG9tZXJJbmZvID0gew0KICAgICAgICBjdXN0b21lcklkOiByb3cuY3VzdG9tZXJJZCwNCiAgICAgICAgYXBwbHlJZDogcm93LmFwcGx5SWQsDQogICAgICAgIGN1c3RvbWVyTmFtZTogcm93LmN1c3RvbWVyTmFtZQ0KICAgICAgfQ0KICAgICAgdGhpcy51c2VySW5mb1Zpc2libGUgPSB0cnVlDQogICAgfSwNCg0KICAgIC8qKiDlpITnkIbml6XmnJ/ojIPlm7Tlj5jljJYgKi8NCiAgICBoYW5kbGVEYXRlUmFuZ2VDaGFuZ2UoZGF0ZXMpIHsNCiAgICAgIGlmIChkYXRlcyAmJiBkYXRlcy5sZW5ndGggPT09IDIpIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zdGFydFRpbWUgPSBkYXRlc1swXQ0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmVuZFRpbWUgPSBkYXRlc1sxXQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zdGFydFRpbWUgPSBudWxsDQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZW5kVGltZSA9IG51bGwNCiAgICAgIH0NCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgIH0sDQoNCiAgICAvKiog5aSE55CG5a6h5om55pe26Ze06IyD5Zu05Y+Y5YyWICovDQogICAgaGFuZGxlQXBwcm92YWxEYXRlUmFuZ2VDaGFuZ2UoZGF0ZXMpIHsNCiAgICAgIGlmIChkYXRlcyAmJiBkYXRlcy5sZW5ndGggPT09IDIpIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5hcHByb3ZhbFN0YXJ0VGltZSA9IGRhdGVzWzBdDQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXBwcm92YWxFbmRUaW1lID0gZGF0ZXNbMV0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXBwcm92YWxTdGFydFRpbWUgPSBudWxsDQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXBwcm92YWxFbmRUaW1lID0gbnVsbA0KICAgICAgfQ0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpDQogICAgfSwNCg0KICAgIC8qKiDojrflj5bnirbmgIHmlofmnKwgKi8NCiAgICBnZXRTdGF0dXNUZXh0KHN0YXR1cykgew0KICAgICAgcmV0dXJuIEFwcHJvdmFsTWFuYWdlci5nZXRTdGF0dXNUZXh0KHN0YXR1cykNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPlueKtuaAgeagh+etvuexu+WeiyAqLw0KICAgIGdldFN0YXR1c1RhZ1R5cGUoc3RhdHVzKSB7DQogICAgICByZXR1cm4gQXBwcm92YWxNYW5hZ2VyLmdldFN0YXR1c1RhZ1R5cGUoc3RhdHVzKQ0KICAgIH0sDQoNCiAgICAvKiog5qOA5p+l5piv5ZCm5Y+v5Lul5a6h5om5ICovDQogICAgY2FuQXBwcm92ZShzdGF0dXMpIHsNCiAgICAgIHJldHVybiBBcHByb3ZhbE1hbmFnZXIuY2FuQXBwcm92ZShzdGF0dXMpDQogICAgfSwNCg0KICAgIC8qKiDmo4Dmn6XmmK/lkKbkuLrmnIDnu4jnirbmgIEgKi8NCiAgICBpc0ZpbmFsU3RhdHVzKHN0YXR1cykgew0KICAgICAgcmV0dXJuIEFwcHJvdmFsTWFuYWdlci5pc0ZpbmFsU3RhdHVzKHN0YXR1cykNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPluWuoeaJueeKtuaAgeeahOaVtOaVsOWAvCAqLw0KICAgIGdldEFwcHJvdmFsU3RhdHVzSW50KHJlY29yZCkgew0KICAgICAgY29uc3Qgc3RhdHVzID0gcmVjb3JkLmFwcHJvdmFsU3RhdHVzDQogICAgICByZXR1cm4gc3RhdHVzID8gcGFyc2VJbnQoc3RhdHVzKSA6IDANCiAgICB9LA0KDQogICAgLyoqIOajgOafpeiusOW9leaYr+WQpuWPr+S7peWuoeaJuSAqLw0KICAgIGNhbkFwcHJvdmVSZWNvcmQocmVjb3JkKSB7DQogICAgICAvLyDlj6rmnInmnKrlrqHmibnnmoTorrDlvZXlj6/ku6XlrqHmibkNCiAgICAgIHJldHVybiByZWNvcmQuYXBwcm92YWxTdGF0dXMgPT09ICcwJyB8fCByZWNvcmQuYXBwcm92YWxTdGF0dXMgPT09IG51bGwgfHwgcmVjb3JkLmFwcHJvdmFsU3RhdHVzID09PSAnJw0KICAgIH0sDQoNCiAgICAvKiog5byA5aeL5a6h5om55rWB56iLICovDQogICAgaGFuZGxlU3RhcnRBcHByb3ZhbEZsb3cocmVjb3JkKSB7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfnoa7orqTopoHlvIDlp4vlrqHmibnmtYHnqIvlkJfvvJ8nKS50aGVuKCgpID0+IHsNCiAgICAgICAgc3RhcnRMaXRpZ2F0aW9uQXBwcm92YWxGbG93KHJlY29yZC5pZCkudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygn5a6h5om55rWB56iL5bey5byA5aeLJykNCiAgICAgICAgICB0aGlzLmxvYWRTdWJtaXNzaW9uUmVjb3Jkcyh0aGlzLmN1cnJlbnRSZWNvcmQubGl0aWdhdGlvbkNhc2VJZCkNCiAgICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgICB9KQ0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pDQogICAgfSwNCg0KICAgIC8qKiDlrqHmibnmtYHnqIvmk43kvZwgKi8NCiAgICBoYW5kbGVGbG93QXBwcm92ZShyZWNvcmQsIGFjdGlvbikgew0KICAgICAgdGhpcy5mbG93QXBwcm92YWxGb3JtLmlkID0gcmVjb3JkLmlkDQogICAgICB0aGlzLmZsb3dBcHByb3ZhbEZvcm0uYWN0aW9uID0gYWN0aW9uDQogICAgICB0aGlzLmZsb3dBcHByb3ZhbEZvcm0uY3VycmVudFN0YXR1cyA9IHRoaXMuZ2V0QXBwcm92YWxTdGF0dXNJbnQocmVjb3JkKQ0KICAgICAgdGhpcy5mbG93QXBwcm92YWxGb3JtLnJlamVjdFJlYXNvbiA9ICcnDQogICAgICB0aGlzLmZsb3dBcHByb3ZhbE9wZW4gPSB0cnVlDQogICAgfSwNCg0KICAgIC8qKiDnoa7orqTlrqHmibnmtYHnqIsgKi8NCiAgICBjb25maXJtRmxvd0FwcHJvdmFsKCkgew0KICAgICAgaWYgKHRoaXMuZmxvd0FwcHJvdmFsRm9ybS5hY3Rpb24gPT09ICdyZWplY3QnICYmICF0aGlzLmZsb3dBcHByb3ZhbEZvcm0ucmVqZWN0UmVhc29uKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfor7fovpPlhaXmi5Lnu53ljp/lm6AnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgY29uc3QgZGF0YSA9IHsNCiAgICAgICAgaWQ6IHRoaXMuZmxvd0FwcHJvdmFsRm9ybS5pZCwNCiAgICAgICAgY3VycmVudFN0YXR1czogdGhpcy5mbG93QXBwcm92YWxGb3JtLmN1cnJlbnRTdGF0dXMsDQogICAgICAgIHJlamVjdFJlYXNvbjogdGhpcy5mbG93QXBwcm92YWxGb3JtLnJlamVjdFJlYXNvbg0KICAgICAgfQ0KDQogICAgICBjb25zdCBhcGlDYWxsID0gdGhpcy5mbG93QXBwcm92YWxGb3JtLmFjdGlvbiA9PT0gJ2FwcHJvdmUnDQogICAgICAgID8gYXBwcm92ZUxpdGlnYXRpb25Db3N0RmxvdyhkYXRhKQ0KICAgICAgICA6IHJlamVjdExpdGlnYXRpb25Db3N0RmxvdyhkYXRhKQ0KDQogICAgICBhcGlDYWxsLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKGAke3RoaXMuZmxvd0FwcHJvdmFsRm9ybS5hY3Rpb24gPT09ICdhcHByb3ZlJyA/ICfpgJrov4cnIDogJ+aLkue7nSd95a6h5om55oiQ5YqfYCkNCiAgICAgICAgdGhpcy5mbG93QXBwcm92YWxPcGVuID0gZmFsc2UNCiAgICAgICAgdGhpcy5sb2FkU3VibWlzc2lvblJlY29yZHModGhpcy5jdXJyZW50UmVjb3JkLmxpdGlnYXRpb25DYXNlSWQpDQogICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvKiog5om56YeP5byA5aeL5a6h5om55rWB56iLICovDQogICAgaGFuZGxlQmF0Y2hTdGFydEFwcHJvdmFsRmxvdygpIHsNCiAgICAgIGNvbnN0IHN0YXJ0UmVjb3JkcyA9IHRoaXMuc2VsZWN0ZWRSZWNvcmRzLmZpbHRlcihyZWNvcmQgPT4gdGhpcy5nZXRBcHByb3ZhbFN0YXR1c0ludChyZWNvcmQpID09PSAwKQ0KICAgICAgaWYgKHN0YXJ0UmVjb3Jkcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+ayoeacieWPr+S7peW8gOWni+WuoeaJueeahOiusOW9lScpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKGDnoa7orqTopoHmibnph4/lvIDlp4sgJHtzdGFydFJlY29yZHMubGVuZ3RofSDmnaHorrDlvZXnmoTlrqHmibnmtYHnqIvlkJfvvJ9gKS50aGVuKCgpID0+IHsNCiAgICAgICAgY29uc3QgcHJvbWlzZXMgPSBzdGFydFJlY29yZHMubWFwKHJlY29yZCA9PiBzdGFydExpdGlnYXRpb25BcHByb3ZhbEZsb3cocmVjb3JkLmlkKSkNCiAgICAgICAgUHJvbWlzZS5hbGwocHJvbWlzZXMpLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoJ+aJuemHj+W8gOWni+WuoeaJuea1geeoi+aIkOWKnycpDQogICAgICAgICAgdGhpcy5sb2FkU3VibWlzc2lvblJlY29yZHModGhpcy5jdXJyZW50UmVjb3JkLmxpdGlnYXRpb25DYXNlSWQpDQogICAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfmibnph4/lvIDlp4vlrqHmibnmtYHnqIvlpLHotKUnKQ0KICAgICAgICB9KQ0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pDQogICAgfSwNCg0KICAgIC8qKiDmibnph4/lrqHmibnmtYHnqIsgKi8NCiAgICBoYW5kbGVCYXRjaEZsb3dBcHByb3ZlKGFjdGlvbikgew0KICAgICAgY29uc3QgYXBwcm92YWxSZWNvcmRzID0gdGhpcy5zZWxlY3RlZFJlY29yZHMuZmlsdGVyKHJlY29yZCA9PiB0aGlzLmNhbkFwcHJvdmUodGhpcy5nZXRBcHByb3ZhbFN0YXR1c0ludChyZWNvcmQpKSkNCiAgICAgIGlmIChhcHByb3ZhbFJlY29yZHMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfmsqHmnInlj6/ku6XlrqHmibnnmoTorrDlvZUnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgdGhpcy5iYXRjaEZsb3dBcHByb3ZhbEZvcm0uYWN0aW9uID0gYWN0aW9uDQogICAgICB0aGlzLmJhdGNoRmxvd0FwcHJvdmFsRm9ybS5yZWplY3RSZWFzb24gPSAnJw0KICAgICAgdGhpcy5iYXRjaEZsb3dBcHByb3ZhbE9wZW4gPSB0cnVlDQogICAgfSwNCg0KICAgIC8qKiDnoa7orqTmibnph4/lrqHmibnmtYHnqIsgKi8NCiAgICBjb25maXJtQmF0Y2hGbG93QXBwcm92YWwoKSB7DQogICAgICBpZiAodGhpcy5iYXRjaEZsb3dBcHByb3ZhbEZvcm0uYWN0aW9uID09PSAncmVqZWN0JyAmJiAhdGhpcy5iYXRjaEZsb3dBcHByb3ZhbEZvcm0ucmVqZWN0UmVhc29uKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfor7fovpPlhaXmi5Lnu53ljp/lm6AnKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgY29uc3QgYXBwcm92YWxSZWNvcmRzID0gdGhpcy5zZWxlY3RlZFJlY29yZHMuZmlsdGVyKHJlY29yZCA9PiB0aGlzLmNhbkFwcHJvdmUodGhpcy5nZXRBcHByb3ZhbFN0YXR1c0ludChyZWNvcmQpKSkNCiAgICAgIGNvbnN0IGRhdGEgPSB7DQogICAgICAgIGlkczogYXBwcm92YWxSZWNvcmRzLm1hcChyZWNvcmQgPT4gcmVjb3JkLmlkKSwNCiAgICAgICAgYWN0aW9uOiB0aGlzLmJhdGNoRmxvd0FwcHJvdmFsRm9ybS5hY3Rpb24sDQogICAgICAgIHJlamVjdFJlYXNvbjogdGhpcy5iYXRjaEZsb3dBcHByb3ZhbEZvcm0ucmVqZWN0UmVhc29uDQogICAgICB9DQoNCiAgICAgIGJhdGNoQXBwcm92ZUxpdGlnYXRpb25Db3N0RmxvdyhkYXRhKS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcyhg5om56YePJHt0aGlzLmJhdGNoRmxvd0FwcHJvdmFsRm9ybS5hY3Rpb24gPT09ICdhcHByb3ZlJyA/ICfpgJrov4cnIDogJ+aLkue7nSd95a6h5om55oiQ5YqfYCkNCiAgICAgICAgdGhpcy5iYXRjaEZsb3dBcHByb3ZhbE9wZW4gPSBmYWxzZQ0KICAgICAgICB0aGlzLnNlbGVjdGVkUmVjb3JkcyA9IFtdDQogICAgICAgIHRoaXMubG9hZFN1Ym1pc3Npb25SZWNvcmRzKHRoaXMuY3VycmVudFJlY29yZC5saXRpZ2F0aW9uQ2FzZUlkKQ0KICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLyoqIOajgOafpeaYr+WQpuWPr+S7peaJuemHj+W8gOWni+WuoeaJuSAqLw0KICAgIGNhbkJhdGNoU3RhcnRBcHByb3ZhbCgpIHsNCiAgICAgIHJldHVybiB0aGlzLnNlbGVjdGVkUmVjb3Jkcy5zb21lKHJlY29yZCA9PiB0aGlzLmdldEFwcHJvdmFsU3RhdHVzSW50KHJlY29yZCkgPT09IDApDQogICAgfSwNCg0KICAgIC8qKiDmo4Dmn6XmmK/lkKblj6/ku6Xmibnph4/lrqHmibkgKi8NCiAgICBjYW5CYXRjaEFwcHJvdmUoKSB7DQogICAgICByZXR1cm4gdGhpcy5zZWxlY3RlZFJlY29yZHMuc29tZShyZWNvcmQgPT4gdGhpcy5jYW5BcHByb3ZlKHRoaXMuZ2V0QXBwcm92YWxTdGF0dXNJbnQocmVjb3JkKSkpDQogICAgfSwNCg0KICAgIC8qKiDojrflj5blj6/ku6XlvIDlp4vlrqHmibnnmoTorrDlvZXmlbDph48gKi8NCiAgICBnZXRTdGFydEFwcHJvdmFsQ291bnQoKSB7DQogICAgICByZXR1cm4gdGhpcy5zZWxlY3RlZFJlY29yZHMuZmlsdGVyKHJlY29yZCA9PiB0aGlzLmdldEFwcHJvdmFsU3RhdHVzSW50KHJlY29yZCkgPT09IDApLmxlbmd0aA0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W5Y+v5Lul5a6h5om555qE6K6w5b2V5pWw6YePICovDQogICAgZ2V0QXBwcm92YWxDb3VudCgpIHsNCiAgICAgIHJldHVybiB0aGlzLnNlbGVjdGVkUmVjb3Jkcy5maWx0ZXIocmVjb3JkID0+IHRoaXMuY2FuQXBwcm92ZSh0aGlzLmdldEFwcHJvdmFsU3RhdHVzSW50KHJlY29yZCkpKS5sZW5ndGgNCiAgICB9LA0KDQogICAgLyoqIOWNleS4quWuoeaJuSAqLw0KICAgIGhhbmRsZVNpbmdsZUFwcHJvdmUocmVjb3JkLCBhY3Rpb24pIHsNCiAgICAgIHRoaXMuc2luZ2xlQXBwcm92YWxGb3JtLmlkID0gcmVjb3JkLmlkDQogICAgICB0aGlzLnNpbmdsZUFwcHJvdmFsRm9ybS5hY3Rpb24gPSBhY3Rpb24NCiAgICAgIHRoaXMuc2luZ2xlQXBwcm92YWxGb3JtLnJlamVjdFJlYXNvbiA9ICcnDQogICAgICB0aGlzLnNpbmdsZUFwcHJvdmFsRGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KDQogICAgLyoqIOehruiupOWNleS4quWuoeaJuSAqLw0KICAgIGNvbmZpcm1TaW5nbGVBcHByb3ZhbCgpIHsNCiAgICAgIGlmICh0aGlzLnNpbmdsZUFwcHJvdmFsRm9ybS5hY3Rpb24gPT09ICdyZWplY3QnKSB7DQogICAgICAgIHRoaXMuJHJlZnNbInNpbmdsZUFwcHJvdmFsRm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgICBpZiAoIXZhbGlkKSByZXR1cm4NCiAgICAgICAgICB0aGlzLmV4ZWN1dGVTaW5nbGVBcHByb3ZhbCgpDQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmV4ZWN1dGVTaW5nbGVBcHByb3ZhbCgpDQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDmiafooYzljZXkuKrlrqHmibkgKi8NCiAgICBleGVjdXRlU2luZ2xlQXBwcm92YWwoKSB7DQogICAgICBjb25zdCBkYXRhID0gew0KICAgICAgICBpZDogdGhpcy5zaW5nbGVBcHByb3ZhbEZvcm0uaWQsDQogICAgICAgIHN0YXR1czogdGhpcy5zaW5nbGVBcHByb3ZhbEZvcm0uYWN0aW9uID09PSAnYXBwcm92ZScgPyAnMScgOiAnNycsDQogICAgICAgIHJlamVjdFJlYXNvbjogdGhpcy5zaW5nbGVBcHByb3ZhbEZvcm0ucmVqZWN0UmVhc29uDQogICAgICB9DQoNCiAgICAgIGFwcHJvdmVMaXRpZ2F0aW9uQ29zdFJlY29yZChkYXRhKS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2VzcyhgJHt0aGlzLnNpbmdsZUFwcHJvdmFsRm9ybS5hY3Rpb24gPT09ICdhcHByb3ZlJyA/ICfpgJrov4cnIDogJ+aLkue7nSd95a6h5om55oiQ5YqfYCkNCiAgICAgICAgdGhpcy5zaW5nbGVBcHByb3ZhbERpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgICAgICB0aGlzLmxvYWRTdWJtaXNzaW9uUmVjb3Jkcyh0aGlzLmN1cnJlbnRSZWNvcmQubGl0aWdhdGlvbkNhc2VJZCkNCiAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgIH0pLmNhdGNoKChlcnJvcikgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCflrqHmibnlpLHotKU6JywgZXJyb3IpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvKiog5om56YeP5a6h5om5ICovDQogICAgaGFuZGxlQmF0Y2hBcHByb3ZlKGFjdGlvbikgew0KICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRSZWNvcmRzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign6K+36YCJ5oup6KaB5a6h5om555qE6K6w5b2VJykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIHRoaXMuYmF0Y2hBcHByb3ZhbEZvcm0uYWN0aW9uID0gYWN0aW9uDQogICAgICB0aGlzLmJhdGNoQXBwcm92YWxGb3JtLnJlamVjdFJlYXNvbiA9ICcnDQogICAgICB0aGlzLmJhdGNoQXBwcm92YWxEaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgIH0sDQoNCiAgICAvKiog56Gu6K6k5om56YeP5a6h5om5ICovDQogICAgY29uZmlybUJhdGNoQXBwcm92YWwoKSB7DQogICAgICBpZiAodGhpcy5iYXRjaEFwcHJvdmFsRm9ybS5hY3Rpb24gPT09ICdyZWplY3QnKSB7DQogICAgICAgIHRoaXMuJHJlZnNbImJhdGNoQXBwcm92YWxGb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICAgIGlmICghdmFsaWQpIHJldHVybg0KICAgICAgICAgIHRoaXMuZXhlY3V0ZUJhdGNoQXBwcm92YWwoKQ0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5leGVjdXRlQmF0Y2hBcHByb3ZhbCgpDQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDmiafooYzmibnph4/lrqHmibkgKi8NCiAgICBleGVjdXRlQmF0Y2hBcHByb3ZhbCgpIHsNCiAgICAgIGNvbnN0IGRhdGEgPSB7DQogICAgICAgIGlkczogdGhpcy5zZWxlY3RlZFJlY29yZHMubWFwKHJlY29yZCA9PiByZWNvcmQuaWQpLA0KICAgICAgICBzdGF0dXM6IHRoaXMuYmF0Y2hBcHByb3ZhbEZvcm0uYWN0aW9uID09PSAnYXBwcm92ZScgPyAnMScgOiAnNycsDQogICAgICAgIHJlamVjdFJlYXNvbjogdGhpcy5iYXRjaEFwcHJvdmFsRm9ybS5yZWplY3RSZWFzb24NCiAgICAgIH0NCg0KICAgICAgYmF0Y2hBcHByb3ZlTGl0aWdhdGlvbkNvc3RSZWNvcmRzKGRhdGEpLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKGDmibnph48ke3RoaXMuYmF0Y2hBcHByb3ZhbEZvcm0uYWN0aW9uID09PSAnYXBwcm92ZScgPyAn6YCa6L+HJyA6ICfmi5Lnu50nfeWuoeaJueaIkOWKn2ApDQogICAgICAgIHRoaXMuYmF0Y2hBcHByb3ZhbERpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgICAgICB0aGlzLnNlbGVjdGVkUmVjb3JkcyA9IFtdDQogICAgICAgIHRoaXMubG9hZFN1Ym1pc3Npb25SZWNvcmRzKHRoaXMuY3VycmVudFJlY29yZC5saXRpZ2F0aW9uQ2FzZUlkKQ0KICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgfSkuY2F0Y2goKGVycm9yKSA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aJuemHj+WuoeaJueWksei0pTonLCBlcnJvcikNCiAgICAgIH0pDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["litigation_approval.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAweA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "litigation_approval.vue", "sourceRoot": "src/views/litigation/litigation", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <!-- 1. 贷款人姓名 -->\r\n      <el-form-item label=\"\" prop=\"customerName\">\r\n        <el-input\r\n          v-model=\"queryParams.customerName\"\r\n          placeholder=\"贷款人姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 2. 贷款人身份证号 -->\r\n      <el-form-item label=\"\" prop=\"certId\">\r\n        <el-input\r\n          v-model=\"queryParams.certId\"\r\n          placeholder=\"贷款人身份证号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 3. 出单渠道 -->\r\n      <el-form-item label=\"\" prop=\"jgName\">\r\n        <el-input\r\n          v-model=\"queryParams.jgName\"\r\n          placeholder=\"出单渠道\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 4. 放款银行 -->\r\n      <el-form-item label=\"\" prop=\"lendingBank\">\r\n        <el-input\r\n          v-model=\"queryParams.lendingBank\"\r\n          placeholder=\"放款银行\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 5. 法诉状态(需呈现多级) -->\r\n      <el-form-item label=\"\" prop=\"litigationStatus\">\r\n        <el-cascader\r\n          v-model=\"queryParams.litigationStatus\"\r\n          :options=\"litigationStatusOptions\"\r\n          :props=\"{ expandTrigger: 'hover', value: 'value', label: 'label', children: 'children' }\"\r\n          placeholder=\"法诉状态\"\r\n          clearable\r\n          @change=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 6. 申请人 -->\r\n      <el-form-item label=\"\" prop=\"applicationBy\">\r\n        <el-input\r\n          v-model=\"queryParams.applicationBy\"\r\n          placeholder=\"申请人\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 7. 费用类型 -->\r\n      <el-form-item label=\"\" prop=\"costCategory\">\r\n        <el-select v-model=\"queryParams.costCategory\" placeholder=\"费用类型\" clearable @change=\"handleQuery\">\r\n          <el-option label=\"律师费\" value=\"律师费\" />\r\n          <el-option label=\"诉讼费\" value=\"诉讼费\" />\r\n          <el-option label=\"保全费\" value=\"保全费\" />\r\n          <el-option label=\"执行费\" value=\"执行费\" />\r\n          <el-option label=\"其他费用\" value=\"其他费用\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <!-- 8. 审批状态 -->\r\n      <el-form-item label=\"\" prop=\"approvalStatus\">\r\n        <el-select v-model=\"queryParams.approvalStatus\" placeholder=\"审批状态\" clearable @change=\"handleQuery\">\r\n          <el-option label=\"未审核\" value=\"\" />\r\n          <el-option label=\"已通过\" value=\"0\" />\r\n          <el-option label=\"已拒绝\" value=\"1\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <!-- 9. 申请时间区间 -->\r\n      <el-form-item label=\"\" prop=\"dateRange\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"申请开始日期\"\r\n          end-placeholder=\"申请结束日期\"\r\n          format=\"yyyy-MM-dd\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"handleDateRangeChange\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 10. 审批时间区间 -->\r\n      <el-form-item label=\"\" prop=\"approvalDateRange\">\r\n        <el-date-picker\r\n          v-model=\"approvalDateRange\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"审批开始日期\"\r\n          end-placeholder=\"审批结束日期\"\r\n          format=\"yyyy-MM-dd\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"handleApprovalDateRangeChange\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['vm_car_order:vm_car_order:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleBatchEdit\"\r\n          v-hasPermi=\"['vm_car_order:vm_car_order:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['vm_car_order:vm_car_order:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['vm_car_order:vm_car_order:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"vm_car_orderList\" @selection-change=\"handleSelectionChange\" row-key=\"id\" style=\"width: 100%\" flex=\"right\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" type=\"index\" width=\"60\" fixed />\r\n      <el-table-column label=\"申请人\" align=\"center\" prop=\"applicationBy\" width=\"100\" />\r\n      <el-table-column label=\"最新申请时间\" align=\"center\" prop=\"applicationTime\" width=\"150\" />\r\n      <el-table-column label=\"案件负责人\" align=\"center\" prop=\"curator\" width=\"100\" />\r\n      <el-table-column label=\"提交次数\" align=\"center\" prop=\"submissionCount\" width=\"100\" />\r\n      <el-table-column label=\"法诉状态\" align=\"center\" prop=\"litigationStatus\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{scope.row.litigationStatus == '1'?'待立案':'已邮寄'}}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"贷款人\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            type=\"text\"\r\n            @click=\"openUserInfo(scope.row)\"\r\n            style=\"color: #409EFF;\">\r\n            {{ scope.row.customerName }}\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"出单渠道\" align=\"center\" prop=\"jgName\" />\r\n      <el-table-column label=\"地区\" align=\"center\" prop=\"area\" width=\"80\" />\r\n      <el-table-column label=\"放款银行\" align=\"center\" prop=\"lendingBank\" />\r\n      <el-table-column label=\"法院地\" align=\"center\" prop=\"courtLocation\" />\r\n      <el-table-column label=\"诉讼法院\" align=\"center\" prop=\"commonPleas\" />\r\n      <el-table-column label=\"律师费\" align=\"center\" prop=\"lawyerFee\" width=\"80\" />\r\n      <el-table-column label=\"诉讼费\" align=\"center\" prop=\"litigationFee\" width=\"80\" />\r\n      <el-table-column label=\"保险费\" align=\"center\" prop=\"insurance\" width=\"80\" />\r\n      <el-table-column label=\"保全费\" align=\"center\" prop=\"preservationFee\" width=\"80\" />\r\n      <el-table-column label=\"布控费\" align=\"center\" prop=\"surveillanceFee\" width=\"80\" />\r\n      <el-table-column label=\"公告费\" align=\"center\" prop=\"announcementFee\" width=\"80\" />\r\n      <el-table-column label=\"评估费\" align=\"center\" prop=\"appraisalFee\" width=\"80\" />\r\n      <el-table-column label=\"执行费\" align=\"center\" prop=\"executionFee\" width=\"80\" />\r\n      <el-table-column label=\"特殊通道费\" align=\"center\" prop=\"specialChannelFees\" width=\"100\" />\r\n      <el-table-column label=\"日常报销\" align=\"center\" prop=\"otherAmountsOwed\" width=\"80\" />\r\n      <el-table-column label=\"总费用\" align=\"center\" prop=\"totalMoney\" width=\"100\" />\r\n      <el-table-column label=\"审批状态\" align=\"center\" prop=\"overallApprovalStatus\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"getStatusTagType(scope.row.approvalFlowStatus || 0)\">\r\n            {{ getStatusText(scope.row.approvalFlowStatus || 0) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"审批时间\" align=\"center\" prop=\"approveTime\" width=\"150\" />\r\n      <el-table-column label=\"审批人角色\" align=\"center\" prop=\"approveRole\" />\r\n      <el-table-column label=\"审批人\" align=\"center\" prop=\"approveBy\" />\r\n      <el-table-column label=\"操作\" align=\"center\" width=\"100\" class-name=\"small-padding fixed-width\" fixed=\"right\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['vm_car_order:vm_car_order:edit']\"\r\n          >审批</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 审批对话框 -->\r\n    <el-dialog title=\"法诉费用审批详情\" :visible.sync=\"open\" width=\"1200px\" append-to-body>\r\n      <div class=\"approval-header\">\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <strong>贷款人：</strong>{{ currentRecord.customerName }}\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>案件负责人：</strong>{{ currentRecord.curator }}\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>法院地：</strong>{{ currentRecord.courtLocation }}\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <div class=\"batch-approval-section\" style=\"margin: 20px 0;\">\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"small\"\r\n          :disabled=\"selectedRecords.length === 0 || !canBatchStartApproval()\"\r\n          @click=\"handleBatchStartApprovalFlow()\">\r\n          批量开始审批 ({{ getStartApprovalCount() }})\r\n        </el-button>\r\n        <el-button\r\n          type=\"success\"\r\n          size=\"small\"\r\n          :disabled=\"selectedRecords.length === 0 || !canBatchApprove()\"\r\n          @click=\"handleBatchFlowApprove('approve')\">\r\n          批量通过 ({{ getApprovalCount() }})\r\n        </el-button>\r\n        <el-button\r\n          type=\"danger\"\r\n          size=\"small\"\r\n          :disabled=\"selectedRecords.length === 0 || !canBatchApprove()\"\r\n          @click=\"handleBatchFlowApprove('reject')\">\r\n          批量拒绝 ({{ getApprovalCount() }})\r\n        </el-button>\r\n      </div>\r\n\r\n      <el-table\r\n        :data=\"submissionRecords\"\r\n        @selection-change=\"handleRecordSelectionChange\"\r\n        v-loading=\"recordsLoading\">\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" fixed=\"left\" />\r\n        <el-table-column label=\"提交时间\" align=\"center\" prop=\"applicationTime\" width=\"150\" />\r\n        <el-table-column label=\"提交人\" align=\"center\" prop=\"applicationBy\" width=\"100\" />\r\n        <el-table-column label=\"律师费\" align=\"center\" prop=\"lawyerFee\" width=\"80\" />\r\n        <el-table-column label=\"诉讼费\" align=\"center\" prop=\"litigationFee\" width=\"80\" />\r\n        <el-table-column label=\"保全费\" align=\"center\" prop=\"preservationFee\" width=\"80\" />\r\n        <el-table-column label=\"布控费\" align=\"center\" prop=\"surveillanceFee\" width=\"80\" />\r\n        <el-table-column label=\"公告费\" align=\"center\" prop=\"announcementFee\" width=\"80\" />\r\n        <el-table-column label=\"评估费\" align=\"center\" prop=\"appraisalFee\" width=\"80\" />\r\n        <el-table-column label=\"执行费\" align=\"center\" prop=\"executionFee\" width=\"80\" />\r\n        <el-table-column label=\"违约金\" align=\"center\" prop=\"penalty\" width=\"80\" />\r\n        <el-table-column label=\"担保费\" align=\"center\" prop=\"guaranteeFee\" width=\"80\" />\r\n        <el-table-column label=\"居间费\" align=\"center\" prop=\"intermediaryFee\" width=\"80\" />\r\n        <el-table-column label=\"代偿金\" align=\"center\" prop=\"compensity\" width=\"80\" />\r\n        <el-table-column label=\"判决金额\" align=\"center\" prop=\"judgmentAmount\" width=\"100\" />\r\n        <el-table-column label=\"利息\" align=\"center\" prop=\"interest\" width=\"80\" />\r\n        <el-table-column label=\"其他欠款\" align=\"center\" prop=\"otherAmountsOwed\" width=\"100\" />\r\n        <el-table-column label=\"保险费\" align=\"center\" prop=\"insurance\" width=\"80\" />\r\n        <el-table-column label=\"总费用\" align=\"center\" prop=\"totalMoney\" width=\"100\" />\r\n        <el-table-column label=\"审批状态\" align=\"center\" prop=\"approvalStatus\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getStatusTagType(parseInt(scope.row.approvalStatus || '0'))\">\r\n              {{ getStatusText(parseInt(scope.row.approvalStatus || '0')) }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"审批时间\" align=\"center\" prop=\"approveTime\" width=\"150\" />\r\n        <el-table-column label=\"审批人\" align=\"center\" prop=\"approveBy\" width=\"100\" />\r\n        <el-table-column label=\"拒绝原因\" align=\"center\" prop=\"reasons\" width=\"150\" />\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"200\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <!-- 未开始审批流程 -->\r\n            <el-button\r\n              v-if=\"getApprovalStatusInt(scope.row) === 0\"\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleStartApprovalFlow(scope.row)\">\r\n              开始审批\r\n            </el-button>\r\n            <!-- 审批流程中 -->\r\n            <div v-else-if=\"canApprove(getApprovalStatusInt(scope.row))\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"success\"\r\n                @click=\"handleFlowApprove(scope.row, 'approve')\">\r\n                通过\r\n              </el-button>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                @click=\"handleFlowApprove(scope.row, 'reject')\">\r\n                拒绝\r\n              </el-button>\r\n            </div>\r\n            <!-- 已完成状态 -->\r\n            <div v-else>\r\n              <el-tag :type=\"getStatusTagType(getApprovalStatusInt(scope.row))\" size=\"mini\">\r\n                {{ getStatusText(getApprovalStatusInt(scope.row)) }}\r\n              </el-tag>\r\n              <div v-if=\"scope.row.approveBy\" style=\"font-size: 12px; color: #999; margin-top: 2px;\">\r\n                {{ scope.row.approveBy }}\r\n              </div>\r\n              <div v-if=\"scope.row.approveTime\" style=\"font-size: 12px; color: #999;\">\r\n                {{ scope.row.approveTime }}\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancel\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 单个审批确认对话框 -->\r\n    <el-dialog title=\"审批确认\" :visible.sync=\"singleApprovalOpen\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"singleApprovalForm\" :model=\"singleApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag v-if=\"singleApprovalForm.status == '0'\" type=\"success\">通过</el-tag>\r\n          <el-tag v-else type=\"danger\">拒绝</el-tag>\r\n        </el-form-item>\r\n        <el-form-item label=\"拒绝原因\" v-if=\"singleApprovalForm.status == '1'\">\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n            v-model=\"singleApprovalForm.rejectReason\">\r\n          </el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmSingleApproval\">确 定</el-button>\r\n        <el-button @click=\"singleApprovalOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n\r\n    <!-- 单个审批确认对话框 -->\r\n    <el-dialog title=\"审批确认\" :visible.sync=\"singleApprovalDialogVisible\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"singleApprovalForm\" :model=\"singleApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag :type=\"singleApprovalForm.action === 'approve' ? 'success' : 'danger'\">\r\n            {{ singleApprovalForm.action === 'approve' ? '通过' : '拒绝' }}\r\n          </el-tag>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"拒绝原因\"\r\n          prop=\"rejectReason\"\r\n          v-if=\"singleApprovalForm.action === 'reject'\"\r\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\r\n          <el-input\r\n            v-model=\"singleApprovalForm.rejectReason\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmSingleApproval\">确 定</el-button>\r\n        <el-button @click=\"singleApprovalDialogVisible = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量审批确认对话框 -->\r\n    <el-dialog title=\"批量审批确认\" :visible.sync=\"batchApprovalDialogVisible\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"batchApprovalForm\" :model=\"batchApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag :type=\"batchApprovalForm.action === 'approve' ? 'success' : 'danger'\">\r\n            {{ batchApprovalForm.action === 'approve' ? '批量通过' : '批量拒绝' }}\r\n          </el-tag>\r\n        </el-form-item>\r\n        <el-form-item label=\"选中记录\">\r\n          <span>{{ selectedRecords.length }} 条记录</span>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"拒绝原因\"\r\n          prop=\"rejectReason\"\r\n          v-if=\"batchApprovalForm.action === 'reject'\"\r\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\r\n          <el-input\r\n            v-model=\"batchApprovalForm.rejectReason\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmBatchApproval\">确 定</el-button>\r\n        <el-button @click=\"batchApprovalDialogVisible = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 审批流程确认对话框 -->\r\n    <el-dialog title=\"审批流程确认\" :visible.sync=\"flowApprovalOpen\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"flowApprovalForm\" :model=\"flowApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag v-if=\"flowApprovalForm.action == 'approve'\" type=\"success\">通过</el-tag>\r\n          <el-tag v-else type=\"danger\">拒绝</el-tag>\r\n        </el-form-item>\r\n        <el-form-item label=\"当前状态\">\r\n          <span>{{ getStatusText(flowApprovalForm.currentStatus) }}</span>\r\n        </el-form-item>\r\n        <el-form-item label=\"拒绝原因\" v-if=\"flowApprovalForm.action == 'reject'\">\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n            v-model=\"flowApprovalForm.rejectReason\">\r\n          </el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmFlowApproval\">确 定</el-button>\r\n        <el-button @click=\"flowApprovalOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量审批流程确认对话框 -->\r\n    <el-dialog title=\"批量审批流程确认\" :visible.sync=\"batchFlowApprovalOpen\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"batchFlowApprovalForm\" :model=\"batchFlowApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag v-if=\"batchFlowApprovalForm.action == 'approve'\" type=\"success\">批量通过</el-tag>\r\n          <el-tag v-else type=\"danger\">批量拒绝</el-tag>\r\n        </el-form-item>\r\n        <el-form-item label=\"选中记录\">\r\n          <span>{{ getApprovalCount() }} 条可审批记录</span>\r\n        </el-form-item>\r\n        <el-form-item label=\"拒绝原因\" v-if=\"batchFlowApprovalForm.action == 'reject'\">\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n            v-model=\"batchFlowApprovalForm.rejectReason\">\r\n          </el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmBatchFlowApproval\">确 定</el-button>\r\n        <el-button @click=\"batchFlowApprovalOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 贷款人信息对话框 -->\r\n    <userInfo ref=\"userInfo\" :visible.sync=\"userInfoVisible\" title=\"贷款人信息\" :customerInfo=\"customerInfo\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listLitigationCostApproval,\r\n  getLitigationCostSubmissionRecords,\r\n  approveLitigationCostRecord,\r\n  batchApproveLitigationCostRecords,\r\n  startLitigationApprovalFlow,\r\n  approveLitigationCostFlow,\r\n  rejectLitigationCostFlow,\r\n  batchApproveLitigationCostFlow\r\n} from \"@/api/litigation_cost_approval/litigation_cost_approval\"\r\nimport areaList from \"../../../assets/area.json\"\r\nimport userInfo from '@/layout/components/Dialog/userInfo.vue'\r\nimport ApprovalManager from \"@/utils/approvalStatus\"\r\nexport default {\r\n  name: \"Vm_car_order\",\r\n  components: {\r\n    userInfo,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // VIEW表格数据\r\n      vm_car_orderList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        // 严格按照9个筛选条件\r\n        // 1. 贷款人姓名\r\n        customerName: null,\r\n        // 2. 贷款人身份证号\r\n        certId: null,\r\n        // 3. 出单渠道\r\n        jgName: null,\r\n        // 4. 放款银行\r\n        lendingBank: null,\r\n        // 5. 法诉状态(多级)\r\n        litigationStatus: null,\r\n        // 6. 申请人\r\n        applicationBy: null,\r\n        // 7. 费用类型\r\n        costCategory: null,\r\n        // 8. 审批状态\r\n        approvalStatus: null,\r\n        // 9. 申请时间区间\r\n        startTime: null,\r\n        endTime: null,\r\n        // 10. 审批时间区间\r\n        approvalStartTime: null,\r\n        approvalEndTime: null,\r\n      },\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 审批日期范围\r\n      approvalDateRange: [],\r\n      // 表单参数\r\n      form: {\r\n        id:'',\r\n        status: 0,\r\n        rejectReason:null,\r\n      },\r\n      // 当前审批记录\r\n      currentRecord: {},\r\n      // 费用提交记录列表\r\n      submissionRecords: [],\r\n      // 记录加载状态\r\n      recordsLoading: false,\r\n      // 选中的记录\r\n      selectedRecords: [],\r\n      // 单个审批对话框\r\n      singleApprovalOpen: false,\r\n      singleApprovalForm: {\r\n        id: '',\r\n        status: '0',\r\n        rejectReason: ''\r\n      },\r\n      // 单个审批对话框\r\n      singleApprovalDialogVisible: false,\r\n      singleApprovalForm: {\r\n        id: '',\r\n        action: '', // 'approve' 或 'reject'\r\n        rejectReason: ''\r\n      },\r\n      // 批量审批对话框\r\n      batchApprovalDialogVisible: false,\r\n      batchApprovalForm: {\r\n        action: '', // 'approve' 或 'reject'\r\n        rejectReason: ''\r\n      },\r\n      // 审批流程对话框\r\n      flowApprovalOpen: false,\r\n      flowApprovalForm: {\r\n        id: '',\r\n        action: 'approve', // 'approve' 或 'reject'\r\n        currentStatus: 0,\r\n        rejectReason: ''\r\n      },\r\n      // 批量审批流程对话框\r\n      batchFlowApprovalOpen: false,\r\n      batchFlowApprovalForm: {\r\n        action: 'approve', // 'approve' 或 'reject'\r\n        rejectReason: ''\r\n      },\r\n      // 贷款人信息相关\r\n      userInfoVisible: false,\r\n      customerInfo: {},\r\n      // 法诉状态多级选项\r\n      litigationStatusOptions: [\r\n        {\r\n          value: '起诉',\r\n          label: '起诉',\r\n          children: [\r\n            { value: '起诉-准备材料', label: '准备材料' },\r\n            { value: '起诉-已提交', label: '已提交' },\r\n            { value: '起诉-法院受理', label: '法院受理' }\r\n          ]\r\n        },\r\n        {\r\n          value: '审理',\r\n          label: '审理',\r\n          children: [\r\n            { value: '审理-开庭审理', label: '开庭审理' },\r\n            { value: '审理-等待判决', label: '等待判决' },\r\n            { value: '审理-一审判决', label: '一审判决' }\r\n          ]\r\n        },\r\n        {\r\n          value: '执行',\r\n          label: '执行',\r\n          children: [\r\n            { value: '执行-申请执行', label: '申请执行' },\r\n            { value: '执行-执行中', label: '执行中' },\r\n            { value: '执行-执行完毕', label: '执行完毕' }\r\n          ]\r\n        },\r\n        {\r\n          value: '结案',\r\n          label: '结案',\r\n          children: [\r\n            { value: '结案-胜诉结案', label: '胜诉结案' },\r\n            { value: '结案-败诉结案', label: '败诉结案' },\r\n            { value: '结案-和解结案', label: '和解结案' }\r\n          ]\r\n        }\r\n      ],\r\n      // 表单校验\r\n      rules: {\r\n        keyProvince:'',\r\n        keyCity:'',\r\n        keyBorough:'',\r\n        keyDetailAddress:'',\r\n      },\r\n      provinceList:areaList,\r\n      cityList:[],\r\n      districtList:[]\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    /** 查询法诉费用审批列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listLitigationCostApproval(this.queryParams).then(response => {\r\n        this.vm_car_orderList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id:'',\r\n        status: 0,\r\n        rejectReason:null,\r\n      }\r\n      this.currentRecord = {}\r\n      this.submissionRecords = []\r\n      this.selectedRecords = []\r\n      this.singleApprovalOpen = false\r\n      this.batchApprovalOpen = false\r\n      this.singleApprovalForm = {\r\n        id: '',\r\n        status: '0',\r\n        rejectReason: ''\r\n      }\r\n      this.singleApprovalDialogVisible = false\r\n      this.singleApprovalForm = {\r\n        id: '',\r\n        action: '',\r\n        rejectReason: ''\r\n      }\r\n      this.batchApprovalDialogVisible = false\r\n      this.batchApprovalForm = {\r\n        action: '',\r\n        rejectReason: ''\r\n      }\r\n      this.flowApprovalOpen = false\r\n      this.flowApprovalForm = {\r\n        id: '',\r\n        action: 'approve',\r\n        currentStatus: 0,\r\n        rejectReason: ''\r\n      }\r\n      this.batchFlowApprovalOpen = false\r\n      this.batchFlowApprovalForm = {\r\n        action: 'approve',\r\n        rejectReason: ''\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.approvalDateRange = []\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加VIEW\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.currentRecord = row\r\n      this.loadSubmissionRecords(row.litigationCaseId)\r\n      this.open = true\r\n    },\r\n\r\n    /** 批量修改按钮操作 */\r\n    handleBatchEdit() {\r\n      this.$modal.msgError('请选择单条记录进行审批操作')\r\n    },\r\n\r\n    /** 加载费用提交记录 */\r\n    loadSubmissionRecords(litigationCaseId) {\r\n      this.recordsLoading = true\r\n      getLitigationCostSubmissionRecords(litigationCaseId).then(response => {\r\n        this.submissionRecords = response.data || []\r\n        this.recordsLoading = false\r\n      }).catch(() => {\r\n        this.recordsLoading = false\r\n      })\r\n    },\r\n\r\n    /** 记录选择变化 */\r\n    handleRecordSelectionChange(selection) {\r\n      this.selectedRecords = selection\r\n    },\r\n\r\n    /** 单个审批 */\r\n    handleSingleApprove(record, status) {\r\n      this.singleApprovalForm.id = record.id\r\n      this.singleApprovalForm.status = status\r\n      this.singleApprovalForm.rejectReason = ''\r\n      this.singleApprovalOpen = true\r\n    },\r\n\r\n    /** 确认单个审批 */\r\n    confirmSingleApproval() {\r\n      if (this.singleApprovalForm.status == '1' && !this.singleApprovalForm.rejectReason) {\r\n        this.$modal.msgError('请输入拒绝原因')\r\n        return\r\n      }\r\n\r\n      approveLitigationCostRecord(this.singleApprovalForm).then(() => {\r\n        this.$modal.msgSuccess('审批成功')\r\n        this.singleApprovalOpen = false\r\n        this.loadSubmissionRecords(this.currentRecord.litigationCaseId)\r\n        this.getList() // 刷新主列表\r\n      })\r\n    },\r\n\r\n\r\n\r\n    /** 主列表批量审批 */\r\n    handleBatchApproveMain(status) {\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError('请选择要审批的记录')\r\n        return\r\n      }\r\n\r\n      const statusText = status === '0' ? '通过' : '拒绝'\r\n      this.$modal.confirm(`确认要批量${statusText}选中的 ${this.ids.length} 条记录吗？`).then(() => {\r\n        const data = {\r\n          ids: this.ids,\r\n          status: status,\r\n          rejectReason: status === '1' ? '批量拒绝' : ''\r\n        }\r\n\r\n        return batchApproveLitigationCostRecords(data)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(`批量${statusText}成功`)\r\n        this.getList()\r\n      }).catch(() => {})\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete() {\r\n      this.$modal.confirm('是否确认删除选中的数据项？').then(() => {\r\n        // 这里可以调用删除API，暂时只是提示\r\n        this.$modal.msgSuccess(\"删除功能暂未实现\")\r\n      }).catch(() => {})\r\n    },\r\n\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('litigation_cost_approval/litigation_cost_approval/export', {\r\n        ...this.queryParams\r\n      }, `litigation_cost_approval_${new Date().getTime()}.xlsx`)\r\n    },\r\n\r\n    /** 打开贷款人信息 */\r\n    openUserInfo(row) {\r\n      if (!row.customerId && !row.applyId) {\r\n        this.$modal.msgError('无法获取贷款人信息')\r\n        return\r\n      }\r\n\r\n      this.customerInfo = {\r\n        customerId: row.customerId,\r\n        applyId: row.applyId,\r\n        customerName: row.customerName\r\n      }\r\n      this.userInfoVisible = true\r\n    },\r\n\r\n    /** 处理日期范围变化 */\r\n    handleDateRangeChange(dates) {\r\n      if (dates && dates.length === 2) {\r\n        this.queryParams.startTime = dates[0]\r\n        this.queryParams.endTime = dates[1]\r\n      } else {\r\n        this.queryParams.startTime = null\r\n        this.queryParams.endTime = null\r\n      }\r\n      this.handleQuery()\r\n    },\r\n\r\n    /** 处理审批时间范围变化 */\r\n    handleApprovalDateRangeChange(dates) {\r\n      if (dates && dates.length === 2) {\r\n        this.queryParams.approvalStartTime = dates[0]\r\n        this.queryParams.approvalEndTime = dates[1]\r\n      } else {\r\n        this.queryParams.approvalStartTime = null\r\n        this.queryParams.approvalEndTime = null\r\n      }\r\n      this.handleQuery()\r\n    },\r\n\r\n    /** 获取状态文本 */\r\n    getStatusText(status) {\r\n      return ApprovalManager.getStatusText(status)\r\n    },\r\n\r\n    /** 获取状态标签类型 */\r\n    getStatusTagType(status) {\r\n      return ApprovalManager.getStatusTagType(status)\r\n    },\r\n\r\n    /** 检查是否可以审批 */\r\n    canApprove(status) {\r\n      return ApprovalManager.canApprove(status)\r\n    },\r\n\r\n    /** 检查是否为最终状态 */\r\n    isFinalStatus(status) {\r\n      return ApprovalManager.isFinalStatus(status)\r\n    },\r\n\r\n    /** 获取审批状态的整数值 */\r\n    getApprovalStatusInt(record) {\r\n      const status = record.approvalStatus\r\n      return status ? parseInt(status) : 0\r\n    },\r\n\r\n    /** 检查记录是否可以审批 */\r\n    canApproveRecord(record) {\r\n      // 只有未审批的记录可以审批\r\n      return record.approvalStatus === '0' || record.approvalStatus === null || record.approvalStatus === ''\r\n    },\r\n\r\n    /** 开始审批流程 */\r\n    handleStartApprovalFlow(record) {\r\n      this.$modal.confirm('确认要开始审批流程吗？').then(() => {\r\n        startLitigationApprovalFlow(record.id).then(() => {\r\n          this.$modal.msgSuccess('审批流程已开始')\r\n          this.loadSubmissionRecords(this.currentRecord.litigationCaseId)\r\n          this.getList()\r\n        })\r\n      }).catch(() => {})\r\n    },\r\n\r\n    /** 审批流程操作 */\r\n    handleFlowApprove(record, action) {\r\n      this.flowApprovalForm.id = record.id\r\n      this.flowApprovalForm.action = action\r\n      this.flowApprovalForm.currentStatus = this.getApprovalStatusInt(record)\r\n      this.flowApprovalForm.rejectReason = ''\r\n      this.flowApprovalOpen = true\r\n    },\r\n\r\n    /** 确认审批流程 */\r\n    confirmFlowApproval() {\r\n      if (this.flowApprovalForm.action === 'reject' && !this.flowApprovalForm.rejectReason) {\r\n        this.$modal.msgError('请输入拒绝原因')\r\n        return\r\n      }\r\n\r\n      const data = {\r\n        id: this.flowApprovalForm.id,\r\n        currentStatus: this.flowApprovalForm.currentStatus,\r\n        rejectReason: this.flowApprovalForm.rejectReason\r\n      }\r\n\r\n      const apiCall = this.flowApprovalForm.action === 'approve'\r\n        ? approveLitigationCostFlow(data)\r\n        : rejectLitigationCostFlow(data)\r\n\r\n      apiCall.then(() => {\r\n        this.$modal.msgSuccess(`${this.flowApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\r\n        this.flowApprovalOpen = false\r\n        this.loadSubmissionRecords(this.currentRecord.litigationCaseId)\r\n        this.getList()\r\n      })\r\n    },\r\n\r\n    /** 批量开始审批流程 */\r\n    handleBatchStartApprovalFlow() {\r\n      const startRecords = this.selectedRecords.filter(record => this.getApprovalStatusInt(record) === 0)\r\n      if (startRecords.length === 0) {\r\n        this.$modal.msgError('没有可以开始审批的记录')\r\n        return\r\n      }\r\n\r\n      this.$modal.confirm(`确认要批量开始 ${startRecords.length} 条记录的审批流程吗？`).then(() => {\r\n        const promises = startRecords.map(record => startLitigationApprovalFlow(record.id))\r\n        Promise.all(promises).then(() => {\r\n          this.$modal.msgSuccess('批量开始审批流程成功')\r\n          this.loadSubmissionRecords(this.currentRecord.litigationCaseId)\r\n          this.getList()\r\n        }).catch(() => {\r\n          this.$modal.msgError('批量开始审批流程失败')\r\n        })\r\n      }).catch(() => {})\r\n    },\r\n\r\n    /** 批量审批流程 */\r\n    handleBatchFlowApprove(action) {\r\n      const approvalRecords = this.selectedRecords.filter(record => this.canApprove(this.getApprovalStatusInt(record)))\r\n      if (approvalRecords.length === 0) {\r\n        this.$modal.msgError('没有可以审批的记录')\r\n        return\r\n      }\r\n\r\n      this.batchFlowApprovalForm.action = action\r\n      this.batchFlowApprovalForm.rejectReason = ''\r\n      this.batchFlowApprovalOpen = true\r\n    },\r\n\r\n    /** 确认批量审批流程 */\r\n    confirmBatchFlowApproval() {\r\n      if (this.batchFlowApprovalForm.action === 'reject' && !this.batchFlowApprovalForm.rejectReason) {\r\n        this.$modal.msgError('请输入拒绝原因')\r\n        return\r\n      }\r\n\r\n      const approvalRecords = this.selectedRecords.filter(record => this.canApprove(this.getApprovalStatusInt(record)))\r\n      const data = {\r\n        ids: approvalRecords.map(record => record.id),\r\n        action: this.batchFlowApprovalForm.action,\r\n        rejectReason: this.batchFlowApprovalForm.rejectReason\r\n      }\r\n\r\n      batchApproveLitigationCostFlow(data).then(() => {\r\n        this.$modal.msgSuccess(`批量${this.batchFlowApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\r\n        this.batchFlowApprovalOpen = false\r\n        this.selectedRecords = []\r\n        this.loadSubmissionRecords(this.currentRecord.litigationCaseId)\r\n        this.getList()\r\n      })\r\n    },\r\n\r\n    /** 检查是否可以批量开始审批 */\r\n    canBatchStartApproval() {\r\n      return this.selectedRecords.some(record => this.getApprovalStatusInt(record) === 0)\r\n    },\r\n\r\n    /** 检查是否可以批量审批 */\r\n    canBatchApprove() {\r\n      return this.selectedRecords.some(record => this.canApprove(this.getApprovalStatusInt(record)))\r\n    },\r\n\r\n    /** 获取可以开始审批的记录数量 */\r\n    getStartApprovalCount() {\r\n      return this.selectedRecords.filter(record => this.getApprovalStatusInt(record) === 0).length\r\n    },\r\n\r\n    /** 获取可以审批的记录数量 */\r\n    getApprovalCount() {\r\n      return this.selectedRecords.filter(record => this.canApprove(this.getApprovalStatusInt(record))).length\r\n    },\r\n\r\n    /** 单个审批 */\r\n    handleSingleApprove(record, action) {\r\n      this.singleApprovalForm.id = record.id\r\n      this.singleApprovalForm.action = action\r\n      this.singleApprovalForm.rejectReason = ''\r\n      this.singleApprovalDialogVisible = true\r\n    },\r\n\r\n    /** 确认单个审批 */\r\n    confirmSingleApproval() {\r\n      if (this.singleApprovalForm.action === 'reject') {\r\n        this.$refs[\"singleApprovalForm\"].validate(valid => {\r\n          if (!valid) return\r\n          this.executeSingleApproval()\r\n        })\r\n      } else {\r\n        this.executeSingleApproval()\r\n      }\r\n    },\r\n\r\n    /** 执行单个审批 */\r\n    executeSingleApproval() {\r\n      const data = {\r\n        id: this.singleApprovalForm.id,\r\n        status: this.singleApprovalForm.action === 'approve' ? '1' : '7',\r\n        rejectReason: this.singleApprovalForm.rejectReason\r\n      }\r\n\r\n      approveLitigationCostRecord(data).then(() => {\r\n        this.$modal.msgSuccess(`${this.singleApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\r\n        this.singleApprovalDialogVisible = false\r\n        this.loadSubmissionRecords(this.currentRecord.litigationCaseId)\r\n        this.getList()\r\n      }).catch((error) => {\r\n        console.error('审批失败:', error)\r\n      })\r\n    },\r\n\r\n    /** 批量审批 */\r\n    handleBatchApprove(action) {\r\n      if (this.selectedRecords.length === 0) {\r\n        this.$modal.msgError('请选择要审批的记录')\r\n        return\r\n      }\r\n\r\n      this.batchApprovalForm.action = action\r\n      this.batchApprovalForm.rejectReason = ''\r\n      this.batchApprovalDialogVisible = true\r\n    },\r\n\r\n    /** 确认批量审批 */\r\n    confirmBatchApproval() {\r\n      if (this.batchApprovalForm.action === 'reject') {\r\n        this.$refs[\"batchApprovalForm\"].validate(valid => {\r\n          if (!valid) return\r\n          this.executeBatchApproval()\r\n        })\r\n      } else {\r\n        this.executeBatchApproval()\r\n      }\r\n    },\r\n\r\n    /** 执行批量审批 */\r\n    executeBatchApproval() {\r\n      const data = {\r\n        ids: this.selectedRecords.map(record => record.id),\r\n        status: this.batchApprovalForm.action === 'approve' ? '1' : '7',\r\n        rejectReason: this.batchApprovalForm.rejectReason\r\n      }\r\n\r\n      batchApproveLitigationCostRecords(data).then(() => {\r\n        this.$modal.msgSuccess(`批量${this.batchApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\r\n        this.batchApprovalDialogVisible = false\r\n        this.selectedRecords = []\r\n        this.loadSubmissionRecords(this.currentRecord.litigationCaseId)\r\n        this.getList()\r\n      }).catch((error) => {\r\n        console.error('批量审批失败:', error)\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.approval-header {\r\n  background-color: #f5f7fa;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.batch-approval-section {\r\n  border: 1px solid #e4e7ed;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 10px;\r\n}\r\n\r\n.el-tag {\r\n  margin: 2px;\r\n}\r\n</style>\r\n"]}]}