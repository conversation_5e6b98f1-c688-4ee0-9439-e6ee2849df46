<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['litigation_cost_approval:litigation_cost_approval:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="litigationCostApprovalList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="案件编号" align="center" prop="caseNumber" />
      <el-table-column label="案件名称" align="center" prop="caseName" />
      <el-table-column label="被告姓名" align="center" prop="defendantName" />
      <el-table-column label="律师费" align="center" prop="lawyerFee">
        <template slot-scope="scope">
          ￥{{ scope.row.lawyerFee || 0 }}
        </template>
      </el-table-column>
      <el-table-column label="诉讼费" align="center" prop="litigationFee">
        <template slot-scope="scope">
          ￥{{ scope.row.litigationFee || 0 }}
        </template>
      </el-table-column>
      <el-table-column label="保全费" align="center" prop="preservationFee">
        <template slot-scope="scope">
          ￥{{ scope.row.preservationFee || 0 }}
        </template>
      </el-table-column>
      <el-table-column label="总费用" align="center" prop="totalMoney">
        <template slot-scope="scope">
          ￥{{ scope.row.totalMoney || 0 }}
        </template>
      </el-table-column>
      <el-table-column label="审批状态" align="center" prop="approvalStatus">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.approvalStatus)">
            {{ getStatusText(scope.row.approvalStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center" prop="applicationTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.applicationTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewDetails(scope.row)"
            v-hasPermi="['litigation_cost_approval:litigation_cost_approval:approve']"
          >审批</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 费用详情审批对话框 -->
    <el-dialog title="法诉费用审批" :visible.sync="detailsDialogVisible" width="1200px" append-to-body>
      <!-- 案件基本信息 -->
      <el-descriptions :column="3" border style="margin-bottom: 20px" v-if="currentCaseInfo">
        <el-descriptions-item label="案件编号">{{ currentCaseInfo.caseNumber }}</el-descriptions-item>
        <el-descriptions-item label="案件名称">{{ currentCaseInfo.caseName }}</el-descriptions-item>
        <el-descriptions-item label="被告姓名">{{ currentCaseInfo.defendantName }}</el-descriptions-item>
        <el-descriptions-item label="被告身份证">{{ currentCaseInfo.defendantIdCard }}</el-descriptions-item>
        <el-descriptions-item label="法院管辖">{{ currentCaseInfo.courtLocation }}</el-descriptions-item>
        <el-descriptions-item label="案件书记员">{{ currentCaseInfo.curator }}</el-descriptions-item>
      </el-descriptions>

      <!-- 费用记录列表 -->
      <div style="margin-bottom: 20px;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
          <h4>费用记录</h4>
          <div>
            <el-button
              type="success"
              size="small"
              @click="handleBatchApprove('approve')"
              :disabled="selectedRecords.length === 0"
            >批量通过</el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleBatchApprove('reject')"
              :disabled="selectedRecords.length === 0"
            >批量拒绝</el-button>
          </div>
        </div>

        <el-table
          :data="feeRecords"
          @selection-change="handleRecordSelectionChange"
          v-loading="recordsLoading"
          border>
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="申请时间" align="center" prop="applicationTime" width="150">
            <template slot-scope="scope">
              {{ parseTime(scope.row.applicationTime, '{y}-{m}-{d} {h}:{i}') }}
            </template>
          </el-table-column>
          <el-table-column label="申请人" align="center" prop="applicationBy" width="100" />
          <el-table-column label="律师费" align="center" prop="lawyerFee" width="80">
            <template slot-scope="scope">
              ￥{{ scope.row.lawyerFee || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="诉讼费" align="center" prop="litigationFee" width="80">
            <template slot-scope="scope">
              ￥{{ scope.row.litigationFee || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="保全费" align="center" prop="preservationFee" width="80">
            <template slot-scope="scope">
              ￥{{ scope.row.preservationFee || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="布控费" align="center" prop="surveillanceFee" width="80">
            <template slot-scope="scope">
              ￥{{ scope.row.surveillanceFee || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="公告费" align="center" prop="announcementFee" width="80">
            <template slot-scope="scope">
              ￥{{ scope.row.announcementFee || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="评估费" align="center" prop="appraisalFee" width="80">
            <template slot-scope="scope">
              ￥{{ scope.row.appraisalFee || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="执行费" align="center" prop="executionFee" width="80">
            <template slot-scope="scope">
              ￥{{ scope.row.executionFee || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="总费用" align="center" prop="totalMoney" width="100">
            <template slot-scope="scope">
              ￥{{ scope.row.totalMoney || 0 }}
            </template>
          </el-table-column>
          <el-table-column label="审批状态" align="center" prop="approvalStatus" width="120">
            <template slot-scope="scope">
              <el-tag :type="getStatusTagType(scope.row.approvalStatus)">
                {{ getStatusText(scope.row.approvalStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="审批时间" align="center" prop="approveTime" width="150">
            <template slot-scope="scope">
              {{ scope.row.approveTime ? parseTime(scope.row.approveTime, '{y}-{m}-{d} {h}:{i}') : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="审批人" align="center" prop="approveBy" width="100" />
          <el-table-column label="拒绝原因" align="center" prop="reasons" width="150" show-overflow-tooltip />
          <el-table-column label="操作" align="center" width="150" fixed="right">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleSingleApprove(scope.row, 'approve')"
                v-if="canApprove(scope.row.approvalStatus)"
              >通过</el-button>
              <el-button
                size="mini"
                type="text"
                @click="handleSingleApprove(scope.row, 'reject')"
                v-if="canApprove(scope.row.approvalStatus)"
              >拒绝</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailsDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 单个审批确认对话框 -->
    <el-dialog title="审批确认" :visible.sync="singleApprovalDialogVisible" width="400px" append-to-body>
      <el-form ref="singleApprovalForm" :model="singleApprovalForm" label-width="80px">
        <el-form-item label="审批结果">
          <el-tag :type="singleApprovalForm.action === 'approve' ? 'success' : 'danger'">
            {{ singleApprovalForm.action === 'approve' ? '通过' : '拒绝' }}
          </el-tag>
        </el-form-item>
        <el-form-item
          label="拒绝原因"
          prop="rejectReason"
          v-if="singleApprovalForm.action === 'reject'"
          :rules="[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]">
          <el-input
            v-model="singleApprovalForm.rejectReason"
            type="textarea"
            :rows="3"
            placeholder="请输入拒绝原因"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmSingleApproval">确 定</el-button>
        <el-button @click="singleApprovalDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量审批确认对话框 -->
    <el-dialog title="批量审批确认" :visible.sync="batchApprovalDialogVisible" width="400px" append-to-body>
      <el-form ref="batchApprovalForm" :model="batchApprovalForm" label-width="80px">
        <el-form-item label="审批结果">
          <el-tag :type="batchApprovalForm.action === 'approve' ? 'success' : 'danger'">
            {{ batchApprovalForm.action === 'approve' ? '批量通过' : '批量拒绝' }}
          </el-tag>
        </el-form-item>
        <el-form-item label="选中记录">
          <span>{{ selectedRecords.length }} 条记录</span>
        </el-form-item>
        <el-form-item
          label="拒绝原因"
          prop="rejectReason"
          v-if="batchApprovalForm.action === 'reject'"
          :rules="[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]">
          <el-input
            v-model="batchApprovalForm.rejectReason"
            type="textarea"
            :rows="3"
            placeholder="请输入拒绝原因"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmBatchApproval">确 定</el-button>
        <el-button @click="batchApprovalDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listPendingApproval,
  getLitigationCostSubmissionRecords,
  singleApproveLitigationCost,
  batchApproveLitigationCostNew
} from "@/api/litigation_cost_approval/litigation_cost_approval"
import ApprovalManager from "@/utils/approvalStatus"

export default {
  name: "LitigationCostApproval",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 法诉费用审批表格数据
      litigationCostApprovalList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        approvalStatus: null,
      },
      // 详情对话框显示状态
      detailsDialogVisible: false,
      // 当前案件信息
      currentCaseInfo: null,
      // 费用记录列表
      feeRecords: [],
      // 费用记录加载状态
      recordsLoading: false,
      // 选中的费用记录
      selectedRecords: [],
      // 单个审批对话框
      singleApprovalDialogVisible: false,
      singleApprovalForm: {
        id: '',
        action: '', // 'approve' 或 'reject'
        rejectReason: ''
      },
      // 批量审批对话框
      batchApprovalDialogVisible: false,
      batchApprovalForm: {
        action: '', // 'approve' 或 'reject'
        rejectReason: ''
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询法诉费用审批列表 */
    getList() {
      this.loading = true
      listPendingApproval(this.queryParams).then(response => {
        this.litigationCostApprovalList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('litigation_cost_approval/litigation_cost_approval/export', {
        ...this.queryParams
      }, `litigation_cost_approval_${new Date().getTime()}.xlsx`)
    },

    /** 获取状态文本 */
    getStatusText(status) {
      return ApprovalManager.getStatusText(parseInt(status))
    },
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      return ApprovalManager.getStatusTagType(parseInt(status))
    },
    /** 检查是否可以审批 */
    canApprove(status) {
      return ApprovalManager.canApprove(parseInt(status))
    },
    /** 检查是否为最终状态 */
    isFinalStatus(status) {
      return ApprovalManager.isFinalStatus(parseInt(status))
    },

    /** 查看费用详情和审批 */
    handleViewDetails(row) {
      this.currentCaseInfo = row
      this.detailsDialogVisible = true
      this.loadFeeRecords(row.litigationCaseId)
    },

    /** 加载费用记录 */
    loadFeeRecords(litigationCaseId) {
      this.recordsLoading = true
      getLitigationCostSubmissionRecords(litigationCaseId).then(response => {
        this.feeRecords = response.data || []
        this.recordsLoading = false
      }).catch(() => {
        this.recordsLoading = false
        this.$modal.msgError('加载费用记录失败')
      })
    },

    /** 费用记录选择变化 */
    handleRecordSelectionChange(selection) {
      this.selectedRecords = selection
    },

    /** 单个审批 */
    handleSingleApprove(record, action) {
      this.singleApprovalForm.id = record.id
      this.singleApprovalForm.action = action
      this.singleApprovalForm.rejectReason = ''
      this.singleApprovalDialogVisible = true
    },

    /** 确认单个审批 */
    confirmSingleApproval() {
      if (this.singleApprovalForm.action === 'reject') {
        this.$refs["singleApprovalForm"].validate(valid => {
          if (!valid) return
          this.executeSingleApproval()
        })
      } else {
        this.executeSingleApproval()
      }
    },

    /** 执行单个审批 */
    executeSingleApproval() {
      const data = {
        id: this.singleApprovalForm.id,
        action: this.singleApprovalForm.action,
        rejectReason: this.singleApprovalForm.rejectReason
      }

      singleApproveLitigationCost(data).then(() => {
        this.$modal.msgSuccess(`${this.singleApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)
        this.singleApprovalDialogVisible = false
        this.loadFeeRecords(this.currentCaseInfo.litigationCaseId)
        this.getList()
      }).catch(() => {
        this.$modal.msgError('审批失败')
      })
    },

    /** 批量审批 */
    handleBatchApprove(action) {
      if (this.selectedRecords.length === 0) {
        this.$modal.msgError('请选择要审批的记录')
        return
      }

      this.batchApprovalForm.action = action
      this.batchApprovalForm.rejectReason = ''
      this.batchApprovalDialogVisible = true
    },

    /** 确认批量审批 */
    confirmBatchApproval() {
      if (this.batchApprovalForm.action === 'reject') {
        this.$refs["batchApprovalForm"].validate(valid => {
          if (!valid) return
          this.executeBatchApproval()
        })
      } else {
        this.executeBatchApproval()
      }
    },

    /** 执行批量审批 */
    executeBatchApproval() {
      const data = {
        ids: this.selectedRecords.map(record => record.id),
        action: this.batchApprovalForm.action,
        rejectReason: this.batchApprovalForm.rejectReason
      }

      batchApproveLitigationCostNew(data).then(() => {
        this.$modal.msgSuccess(`批量${this.batchApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)
        this.batchApprovalDialogVisible = false
        this.loadFeeRecords(this.currentCaseInfo.litigationCaseId)
        this.getList()
      }).catch(() => {
        this.$modal.msgError('批量审批失败')
      })
    }
  }
}
</script>
