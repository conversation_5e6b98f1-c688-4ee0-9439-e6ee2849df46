package com.ruoyi.litigation_cost_approval.controller;

import java.util.List;
import java.util.Map;
import java.util.Date;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.litigation_cost_approval.domain.LitigationCostApproval;
import com.ruoyi.litigation_cost_approval.domain.dto.SingleApprovalRequest;
import com.ruoyi.litigation_cost_approval.domain.dto.BatchApprovalRequest;
import com.ruoyi.litigation_cost_approval.service.ILitigationCostApprovalService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.service.ISysUserService;

/**
 * 法诉费用审批Controller
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/litigation_cost_approval/litigation_cost_approval")
public class LitigationCostApprovalController extends BaseController
{
    @Autowired
    private ILitigationCostApprovalService litigationCostApprovalService;

    @Autowired
    private ISysUserService sysUserService;

    /**
     * 查询法诉费用审批列表（显示法诉记录，每个费用取最新的展示）
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:list')")
    @GetMapping("/list")
    public TableDataInfo list(LitigationCostApproval litigationCostApproval)
    {
        startPage();
        List<LitigationCostApproval> list = litigationCostApprovalService.selectLitigationCostApprovalList(litigationCostApproval);
        return getDataTable(list);
    }

    /**
     * 导出法诉费用审批列表
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:export')")
    @Log(title = "法诉费用审批", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LitigationCostApproval litigationCostApproval)
    {
        List<LitigationCostApproval> list = litigationCostApprovalService.selectLitigationCostApprovalList(litigationCostApproval);
        ExcelUtil<LitigationCostApproval> util = new ExcelUtil<LitigationCostApproval>(LitigationCostApproval.class);
        util.exportExcel(response, list, "法诉费用审批数据");
    }

    /**
     * 获取法诉费用审批详细信息
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(litigationCostApprovalService.selectLitigationCostApprovalById(id));
    }

    /**
     * 根据法诉案件ID获取费用提交记录详情（用于审批弹窗）
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:query')")
    @GetMapping("/records/{litigationCaseId}")
    public AjaxResult getSubmissionRecords(@PathVariable("litigationCaseId") Long litigationCaseId)
    {
        List<LitigationCostApproval> records = litigationCostApprovalService.selectSubmissionRecordsByLitigationCaseId(litigationCaseId);
        return success(records);
    }

    /**
     * 新增法诉费用审批
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:add')")
    @Log(title = "法诉费用审批", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LitigationCostApproval litigationCostApproval)
    {
        return toAjax(litigationCostApprovalService.insertLitigationCostApproval(litigationCostApproval));
    }

    /**
     * 修改法诉费用审批
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:edit')")
    @Log(title = "法诉费用审批", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LitigationCostApproval litigationCostApproval)
    {
        return toAjax(litigationCostApprovalService.updateLitigationCostApproval(litigationCostApproval));
    }

    /**
     * 删除法诉费用审批
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:remove')")
    @Log(title = "法诉费用审批", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(litigationCostApprovalService.deleteLitigationCostApprovalByIds(ids));
    }

    /**
     * 单个审批费用记录
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:approve')")
    @Log(title = "法诉费用审批", businessType = BusinessType.UPDATE)
    @PutMapping("/approve")
    public AjaxResult approve(@RequestBody SingleApprovalRequest request)
    {
        int result = litigationCostApprovalService.approveLitigationCostRecord(
            request.getId(), request.getStatus(), request.getRejectReason());
        return toAjax(result);
    }

    /**
     * 批量审批费用记录
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:approve')")
    @Log(title = "法诉费用审批", businessType = BusinessType.UPDATE)
    @PutMapping("/batchApprove")
    public AjaxResult batchApprove(@RequestBody BatchApprovalRequest request)
    {
        int result = litigationCostApprovalService.batchApproveLitigationCostRecords(
            request.getIds(), request.getStatus(), request.getRejectReason());
        return toAjax(result);
    }

    /**
     * 获取审批状态统计
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:query')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        List<Map<String, Object>> statistics = litigationCostApprovalService.getApprovalStatistics();
        return success(statistics);
    }

    /**
     * 查询待审批记录数量
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:query')")
    @GetMapping("/pendingCount")
    public AjaxResult getPendingCount()
    {
        int count = litigationCostApprovalService.countPendingApproval();
        return success(count);
    }

    /**
     * 查询待审批列表
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:list')")
    @GetMapping("/list/pending")
    public TableDataInfo listPending()
    {
        String currentUser = getUsername();
        String userRole = sysUserService.selectUserRoleGroup(currentUser);

        startPage();
        List<LitigationCostApproval> list = litigationCostApprovalService.selectPendingApprovalList(userRole);
        return getDataTable(list);
    }

    /**
     * 开始审批流程
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:approve')")
    @Log(title = "开始审批流程", businessType = BusinessType.UPDATE)
    @PostMapping("/startApproval/{id}")
    public AjaxResult startApprovalFlow(@PathVariable("id") Long id)
    {
        if (id == null) {
            return error("审批ID不能为空");
        }

        // 获取审批信息
        LitigationCostApproval approval = litigationCostApprovalService.selectLitigationCostApprovalById(id);
        if (approval == null) {
            return error("找不到对应的审批信息");
        }

        // 检查当前状态是否可以开始审批流程
        if (approval.getApprovalStatus() != null && !"0".equals(approval.getApprovalStatus())) {
            return error("当前状态不允许开始审批流程，当前状态为【" + getStatusText(approval.getApprovalStatus()) + "】");
        }

        // 设置为第一个审批节点
        approval.setApprovalStatus("3"); // 法诉主管审批
        approval.setUpdateBy(getUsername());
        approval.setUpdateTime(new Date());

        return toAjax(litigationCostApprovalService.updateLitigationCostApproval(approval));
    }

    /**
     * 审批通过
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:approve')")
    @Log(title = "审批通过", businessType = BusinessType.UPDATE)
    @PostMapping("/approve")
    public AjaxResult approveRecord(@RequestBody Map<String, Object> params)
    {
        Long id = Long.valueOf(params.get("id").toString());
        String currentStatus = (String) params.get("currentStatus");

        if (id == null) {
            return error("审批ID不能为空");
        }

        // 获取当前用户角色
        String currentUser = getUsername();
        String userRole = sysUserService.selectUserRoleGroup(currentUser);

        // 获取审批信息
        LitigationCostApproval approval = litigationCostApprovalService.selectLitigationCostApprovalById(id);
        if (approval == null) {
            return error("找不到对应的审批信息");
        }

        // 验证当前状态
        if (!approval.getApprovalStatus().equals(currentStatus)) {
            return error("审批状态已发生变化，请刷新页面后重试");
        }

        // 检查是否可以审批
        if (!canUserApprove(approval.getApprovalStatus(), userRole)) {
            return error("您没有权限进行当前阶段的审批，当前状态为【" + getStatusText(approval.getApprovalStatus()) + "】，需要角色：" + getRequiredRole(approval.getApprovalStatus()));
        }

        // 获取下一个状态
        String nextStatus = getNextApprovalStatus(approval.getApprovalStatus());
        approval.setApprovalStatus(nextStatus);
        approval.setApproveTime(new Date());
        approval.setApproveBy(currentUser);
        approval.setApproveRole(userRole);
        approval.setUpdateBy(currentUser);
        approval.setUpdateTime(new Date());
        approval.setReasons(null); // 清除拒绝原因

        return toAjax(litigationCostApprovalService.updateLitigationCostApproval(approval));
    }

    /**
     * 审批拒绝
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:approve')")
    @Log(title = "审批拒绝", businessType = BusinessType.UPDATE)
    @PostMapping("/reject")
    public AjaxResult rejectRecord(@RequestBody Map<String, Object> params)
    {
        Long id = Long.valueOf(params.get("id").toString());
        String currentStatus = (String) params.get("currentStatus");
        String rejectReason = (String) params.get("rejectReason");

        if (id == null) {
            return error("审批ID不能为空");
        }

        if (rejectReason == null || rejectReason.trim().isEmpty()) {
            return error("拒绝原因不能为空");
        }

        // 获取当前用户角色
        String currentUser = getUsername();
        String userRole = sysUserService.selectUserRoleGroup(currentUser);

        // 获取审批信息
        LitigationCostApproval approval = litigationCostApprovalService.selectLitigationCostApprovalById(id);
        if (approval == null) {
            return error("找不到对应的审批信息");
        }

        // 验证当前状态
        if (!approval.getApprovalStatus().equals(currentStatus)) {
            return error("审批状态已发生变化，请刷新页面后重试");
        }

        // 检查是否可以审批
        if (!canUserApprove(approval.getApprovalStatus(), userRole)) {
            return error("您没有权限进行当前阶段的审批，当前状态为【" + getStatusText(approval.getApprovalStatus()) + "】，需要角色：" + getRequiredRole(approval.getApprovalStatus()));
        }

        // 设置为拒绝状态
        approval.setApprovalStatus("2"); // 已拒绝
        approval.setReasons(rejectReason);
        approval.setApproveTime(new Date());
        approval.setApproveBy(currentUser);
        approval.setApproveRole(userRole);
        approval.setUpdateBy(currentUser);
        approval.setUpdateTime(new Date());

        return toAjax(litigationCostApprovalService.updateLitigationCostApproval(approval));
    }

    /**
     * 单个审批费用记录（新版本）
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:approve')")
    @Log(title = "单个审批", businessType = BusinessType.UPDATE)
    @PostMapping("/singleApprove")
    public AjaxResult singleApprove(@RequestBody Map<String, Object> params)
    {
        Long id = Long.valueOf(params.get("id").toString());
        String action = (String) params.get("action");
        String rejectReason = (String) params.get("rejectReason");

        if (id == null) {
            return error("审批ID不能为空");
        }

        if (!"approve".equals(action) && !"reject".equals(action)) {
            return error("无效的审批操作");
        }

        if ("reject".equals(action) && (rejectReason == null || rejectReason.trim().isEmpty())) {
            return error("拒绝审批时必须填写拒绝原因");
        }

        // 获取当前用户信息
        String currentUser = getUsername();
        String userRole = sysUserService.selectUserRoleGroup(currentUser);

        try {
            LitigationCostApproval approval = litigationCostApprovalService.selectLitigationCostApprovalById(id);
            if (approval == null) {
                return error("找不到对应的审批记录");
            }

            // 检查是否可以审批
            if (!canUserApprove(approval.getApprovalStatus(), userRole)) {
                return error("您没有权限进行当前阶段的审批，当前状态为【" + getStatusText(approval.getApprovalStatus()) + "】，需要角色：" + getRequiredRole(approval.getApprovalStatus()));
            }

            // 执行审批操作
            if ("approve".equals(action)) {
                approval.setApprovalStatus(getNextApprovalStatus(approval.getApprovalStatus()));
            } else {
                approval.setApprovalStatus("2"); // 已拒绝
                approval.setReasons(rejectReason);
            }

            approval.setApproveTime(new Date());
            approval.setApproveBy(currentUser);
            approval.setApproveRole(userRole);
            approval.setUpdateBy(currentUser);
            approval.setUpdateTime(new Date());

            litigationCostApprovalService.updateLitigationCostApproval(approval);
            return success("审批成功");

        } catch (Exception e) {
            return error("审批失败：" + e.getMessage());
        }
    }

    /**
     * 批量审批费用记录（新版本）
     */
    @PreAuthorize("@ss.hasPermi('litigation_cost_approval:litigation_cost_approval:approve')")
    @Log(title = "批量审批", businessType = BusinessType.UPDATE)
    @PostMapping("/batchApproveNew")
    public AjaxResult batchApproveNew(@RequestBody Map<String, Object> params)
    {
        @SuppressWarnings("unchecked")
        List<Long> ids = (List<Long>) params.get("ids");
        String action = (String) params.get("action");
        String rejectReason = (String) params.get("rejectReason");

        if (ids == null || ids.isEmpty()) {
            return error("请选择要审批的记录");
        }

        if (!"approve".equals(action) && !"reject".equals(action)) {
            return error("无效的审批操作");
        }

        if ("reject".equals(action) && (rejectReason == null || rejectReason.trim().isEmpty())) {
            return error("拒绝审批时必须填写拒绝原因");
        }

        // 获取当前用户信息
        String currentUser = getUsername();
        String userRole = sysUserService.selectUserRoleGroup(currentUser);

        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMessages = new StringBuilder();

        for (Long id : ids) {
            try {
                LitigationCostApproval approval = litigationCostApprovalService.selectLitigationCostApprovalById(id);
                if (approval == null) {
                    failCount++;
                    errorMessages.append("ID为").append(id).append("的记录不存在；");
                    continue;
                }

                // 检查是否可以审批
                if (!canUserApprove(approval.getApprovalStatus(), userRole)) {
                    failCount++;
                    errorMessages.append("ID为").append(id).append("的记录当前状态不允许审批；");
                    continue;
                }

                // 执行审批操作
                if ("approve".equals(action)) {
                    approval.setApprovalStatus(getNextApprovalStatus(approval.getApprovalStatus()));
                } else {
                    approval.setApprovalStatus("2"); // 已拒绝
                    approval.setReasons(rejectReason);
                }

                approval.setApproveTime(new Date());
                approval.setApproveBy(currentUser);
                approval.setApproveRole(userRole);
                approval.setUpdateBy(currentUser);
                approval.setUpdateTime(new Date());

                litigationCostApprovalService.updateLitigationCostApproval(approval);
                successCount++;

            } catch (Exception e) {
                failCount++;
                errorMessages.append("ID为").append(id).append("的记录审批失败：").append(e.getMessage()).append("；");
            }
        }

        if (failCount == 0) {
            return success("批量审批成功，共处理 " + successCount + " 条记录");
        } else if (successCount == 0) {
            return error("批量审批失败：" + errorMessages.toString());
        } else {
            return success("批量审批完成，成功 " + successCount + " 条，失败 " + failCount + " 条。失败原因：" + errorMessages.toString());
        }
    }

    /**
     * 获取下一个审批状态
     */
    private String getNextApprovalStatus(String currentStatus) {
        if ("3".equals(currentStatus)) { // 法诉主管审批
            return "4"; // 总监审批
        } else if ("4".equals(currentStatus)) { // 总监审批
            return "5"; // 财务主管/总监抄送
        } else if ("5".equals(currentStatus)) { // 财务主管/总监抄送
            return "6"; // 总经理/董事长审批
        } else if ("6".equals(currentStatus)) { // 总经理/董事长审批
            return "1"; // 全部同意
        }
        return "1"; // 默认全部同意
    }

    /**
     * 检查用户是否可以审批当前状态
     */
    private boolean canUserApprove(String status, String userRole) {
        if (status == null || userRole == null) {
            return false;
        }

        switch (status) {
            case "0": // 未审批
                return "法诉主管".equals(userRole);
            case "3": // 法诉主管审批
                return "法诉主管".equals(userRole);
            case "4": // 总监审批
                return "总监".equals(userRole);
            case "5": // 财务主管/总监抄送
                return "财务主管".equals(userRole) || "财务总监".equals(userRole);
            case "6": // 总经理/董事长审批
                return "总经理".equals(userRole) || "董事长".equals(userRole);
            default:
                return false;
        }
    }

    /**
     * 获取状态描述文本
     */
    private String getStatusText(String status) {
        if (status == null) {
            return "未知状态";
        }

        switch (status) {
            case "0":
                return "未审批";
            case "1":
                return "全部同意";
            case "2":
                return "已拒绝";
            case "3":
                return "法诉主管审批";
            case "4":
                return "总监审批";
            case "5":
                return "财务主管/总监抄送";
            case "6":
                return "总经理/董事长审批";
            default:
                return "未知状态";
        }
    }

    /**
     * 获取当前状态需要的审批角色
     */
    private String getRequiredRole(String status) {
        if (status == null) {
            return "未知";
        }

        switch (status) {
            case "0":
                return "法诉主管";
            case "3":
                return "法诉主管";
            case "4":
                return "总监";
            case "5":
                return "财务主管/总监";
            case "6":
                return "总经理/董事长";
            default:
                return "未知";
        }
    }
}
