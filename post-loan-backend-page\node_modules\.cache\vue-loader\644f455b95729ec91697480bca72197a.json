{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation_cost_approval\\litigation_cost_approval\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation_cost_approval\\litigation_cost_approval\\index.vue", "mtime": 1754124414912}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IAogIGxpc3RMaXRpZ2F0aW9uQ29zdEFwcHJvdmFsLCAKICBnZXRMaXRpZ2F0aW9uQ29zdFN1Ym1pc3Npb25SZWNvcmRzLCAKICBzaW5nbGVBcHByb3ZlTGl0aWdhdGlvbkNvc3QsIAogIGJhdGNoQXBwcm92ZUxpdGlnYXRpb25Db3N0TmV3IAp9IGZyb20gIkAvYXBpL2xpdGlnYXRpb25fY29zdF9hcHByb3ZhbC9saXRpZ2F0aW9uX2Nvc3RfYXBwcm92YWwiCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkxpdGlnYXRpb25Db3N0QXBwcm92YWwiLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g5rOV6K+J6LS555So5a6h5om56KGo5qC85pWw5o2uCiAgICAgIGxpdGlnYXRpb25Db3N0QXBwcm92YWxMaXN0OiBbXSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgYXBwcm92YWxTdGF0dXM6IG51bGwsCiAgICAgIH0sCiAgICAgIC8vIOivpuaDheWvueivneahhuaYvuekuueKtuaAgQogICAgICBkZXRhaWxzRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIC8vIOW9k+WJjeahiOS7tuS/oeaBrwogICAgICBjdXJyZW50Q2FzZUluZm86IG51bGwsCiAgICAgIC8vIOi0ueeUqOiusOW9leWIl+ihqAogICAgICBmZWVSZWNvcmRzOiBbXSwKICAgICAgLy8g6LS555So6K6w5b2V5Yqg6L2954q25oCBCiAgICAgIHJlY29yZHNMb2FkaW5nOiBmYWxzZSwKICAgICAgLy8g6YCJ5Lit55qE6LS555So6K6w5b2VCiAgICAgIHNlbGVjdGVkUmVjb3JkczogW10sCiAgICAgIC8vIOWNleS4quWuoeaJueWvueivneahhgogICAgICBzaW5nbGVBcHByb3ZhbERpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBzaW5nbGVBcHByb3ZhbEZvcm06IHsKICAgICAgICBpZDogJycsCiAgICAgICAgYWN0aW9uOiAnJywgLy8gJ2FwcHJvdmUnIOaIliAncmVqZWN0JwogICAgICAgIHJlamVjdFJlYXNvbjogJycKICAgICAgfSwKICAgICAgLy8g5om56YeP5a6h5om55a+56K+d5qGGCiAgICAgIGJhdGNoQXBwcm92YWxEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgYmF0Y2hBcHByb3ZhbEZvcm06IHsKICAgICAgICBhY3Rpb246ICcnLCAvLyAnYXBwcm92ZScg5oiWICdyZWplY3QnCiAgICAgICAgcmVqZWN0UmVhc29uOiAnJwogICAgICB9CiAgICB9CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCkKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6Lms5Xor4notLnnlKjlrqHmibnliJfooaggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUKICAgICAgbGlzdExpdGlnYXRpb25Db3N0QXBwcm92YWwodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5saXRpZ2F0aW9uQ29zdEFwcHJvdmFsTGlzdCA9IHJlc3BvbnNlLnJvd3MKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQogICAgICB9KQogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxCiAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGghPT0xCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aAogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgnbGl0aWdhdGlvbl9jb3N0X2FwcHJvdmFsL2xpdGlnYXRpb25fY29zdF9hcHByb3ZhbC9leHBvcnQnLCB7CiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcwogICAgICB9LCBgbGl0aWdhdGlvbl9jb3N0X2FwcHJvdmFsXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQogICAgfSwKCiAgICAvKiog6I635Y+W54q25oCB5paH5pysICovCiAgICBnZXRTdGF0dXNUZXh0KHN0YXR1cykgewogICAgICBjb25zdCBzdGF0dXNNYXAgPSB7CiAgICAgICAgJzAnOiAn5pyq5a6h5om5JywKICAgICAgICAnMSc6ICflhajpg6jlkIzmhI8nLAogICAgICAgICcyJzogJ+W3suaLkue7nScsCiAgICAgICAgJzMnOiAn5rOV6K+J5Li7566h5a6h5om5JywKICAgICAgICAnNCc6ICfmgLvnm5HlrqHmibknLAogICAgICAgICc1JzogJ+i0ouWKoeS4u+euoS/mgLvnm5HmioTpgIEnLAogICAgICAgICc2JzogJ+aAu+e7j+eQhi/okaPkuovplb/lrqHmibknCiAgICAgIH0KICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICfmnKrnn6XnirbmgIEnCiAgICB9LAogICAgLyoqIOiOt+WPlueKtuaAgeagh+etvuexu+WeiyAqLwogICAgZ2V0U3RhdHVzVGFnVHlwZShzdGF0dXMpIHsKICAgICAgY29uc3QgdHlwZU1hcCA9IHsKICAgICAgICAnMCc6ICdpbmZvJywKICAgICAgICAnMSc6ICdzdWNjZXNzJywKICAgICAgICAnMic6ICdkYW5nZXInLAogICAgICAgICczJzogJ3dhcm5pbmcnLAogICAgICAgICc0JzogJ3dhcm5pbmcnLAogICAgICAgICc1JzogJ3dhcm5pbmcnLAogICAgICAgICc2JzogJ3dhcm5pbmcnCiAgICAgIH0KICAgICAgcmV0dXJuIHR5cGVNYXBbc3RhdHVzXSB8fCAnaW5mbycKICAgIH0sCiAgICAvKiog5qOA5p+l5piv5ZCm5Y+v5Lul5a6h5om5ICovCiAgICBjYW5BcHByb3ZlKHN0YXR1cykgewogICAgICByZXR1cm4gWyczJywgJzQnLCAnNScsICc2J10uaW5jbHVkZXMoc3RhdHVzKQogICAgfSwKCiAgICAvKiog5p+l55yL6LS555So6K+m5oOF5ZKM5a6h5om5ICovCiAgICBoYW5kbGVWaWV3RGV0YWlscyhyb3cpIHsKICAgICAgdGhpcy5jdXJyZW50Q2FzZUluZm8gPSByb3cKICAgICAgdGhpcy5kZXRhaWxzRGlhbG9nVmlzaWJsZSA9IHRydWUKICAgICAgdGhpcy5sb2FkRmVlUmVjb3Jkcyhyb3cubGl0aWdhdGlvbkNhc2VJZCkKICAgIH0sCgogICAgLyoqIOWKoOi9vei0ueeUqOiusOW9lSAqLwogICAgbG9hZEZlZVJlY29yZHMobGl0aWdhdGlvbkNhc2VJZCkgewogICAgICB0aGlzLnJlY29yZHNMb2FkaW5nID0gdHJ1ZQogICAgICBnZXRMaXRpZ2F0aW9uQ29zdFN1Ym1pc3Npb25SZWNvcmRzKGxpdGlnYXRpb25DYXNlSWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZmVlUmVjb3JkcyA9IHJlc3BvbnNlLmRhdGEgfHwgW10KICAgICAgICB0aGlzLnJlY29yZHNMb2FkaW5nID0gZmFsc2UKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMucmVjb3Jkc0xvYWRpbmcgPSBmYWxzZQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfliqDovb3otLnnlKjorrDlvZXlpLHotKUnKQogICAgICB9KQogICAgfSwKCiAgICAvKiog6LS555So6K6w5b2V6YCJ5oup5Y+Y5YyWICovCiAgICBoYW5kbGVSZWNvcmRTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRSZWNvcmRzID0gc2VsZWN0aW9uCiAgICB9LAoKICAgIC8qKiDljZXkuKrlrqHmibkgKi8KICAgIGhhbmRsZVNpbmdsZUFwcHJvdmUocmVjb3JkLCBhY3Rpb24pIHsKICAgICAgdGhpcy5zaW5nbGVBcHByb3ZhbEZvcm0uaWQgPSByZWNvcmQuaWQKICAgICAgdGhpcy5zaW5nbGVBcHByb3ZhbEZvcm0uYWN0aW9uID0gYWN0aW9uCiAgICAgIHRoaXMuc2luZ2xlQXBwcm92YWxGb3JtLnJlamVjdFJlYXNvbiA9ICcnCiAgICAgIHRoaXMuc2luZ2xlQXBwcm92YWxEaWFsb2dWaXNpYmxlID0gdHJ1ZQogICAgfSwKCiAgICAvKiog56Gu6K6k5Y2V5Liq5a6h5om5ICovCiAgICBjb25maXJtU2luZ2xlQXBwcm92YWwoKSB7CiAgICAgIGlmICh0aGlzLnNpbmdsZUFwcHJvdmFsRm9ybS5hY3Rpb24gPT09ICdyZWplY3QnKSB7CiAgICAgICAgdGhpcy4kcmVmc1sic2luZ2xlQXBwcm92YWxGb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgICAgaWYgKCF2YWxpZCkgcmV0dXJuCiAgICAgICAgICB0aGlzLmV4ZWN1dGVTaW5nbGVBcHByb3ZhbCgpCiAgICAgICAgfSkKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmV4ZWN1dGVTaW5nbGVBcHByb3ZhbCgpCiAgICAgIH0KICAgIH0sCgogICAgLyoqIOaJp+ihjOWNleS4quWuoeaJuSAqLwogICAgZXhlY3V0ZVNpbmdsZUFwcHJvdmFsKCkgewogICAgICBjb25zdCBkYXRhID0gewogICAgICAgIGlkOiB0aGlzLnNpbmdsZUFwcHJvdmFsRm9ybS5pZCwKICAgICAgICBhY3Rpb246IHRoaXMuc2luZ2xlQXBwcm92YWxGb3JtLmFjdGlvbiwKICAgICAgICByZWplY3RSZWFzb246IHRoaXMuc2luZ2xlQXBwcm92YWxGb3JtLnJlamVjdFJlYXNvbgogICAgICB9CgogICAgICBzaW5nbGVBcHByb3ZlTGl0aWdhdGlvbkNvc3QoZGF0YSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2VzcyhgJHt0aGlzLnNpbmdsZUFwcHJvdmFsRm9ybS5hY3Rpb24gPT09ICdhcHByb3ZlJyA/ICfpgJrov4cnIDogJ+aLkue7nSd95a6h5om55oiQ5YqfYCkKICAgICAgICB0aGlzLnNpbmdsZUFwcHJvdmFsRGlhbG9nVmlzaWJsZSA9IGZhbHNlCiAgICAgICAgdGhpcy5sb2FkRmVlUmVjb3Jkcyh0aGlzLmN1cnJlbnRDYXNlSW5mby5saXRpZ2F0aW9uQ2FzZUlkKQogICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign5a6h5om55aSx6LSlJykKICAgICAgfSkKICAgIH0sCgogICAgLyoqIOaJuemHj+WuoeaJuSAqLwogICAgaGFuZGxlQmF0Y2hBcHByb3ZlKGFjdGlvbikgewogICAgICBpZiAodGhpcy5zZWxlY3RlZFJlY29yZHMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+ivt+mAieaLqeimgeWuoeaJueeahOiusOW9lScpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIHRoaXMuYmF0Y2hBcHByb3ZhbEZvcm0uYWN0aW9uID0gYWN0aW9uCiAgICAgIHRoaXMuYmF0Y2hBcHByb3ZhbEZvcm0ucmVqZWN0UmVhc29uID0gJycKICAgICAgdGhpcy5iYXRjaEFwcHJvdmFsRGlhbG9nVmlzaWJsZSA9IHRydWUKICAgIH0sCgogICAgLyoqIOehruiupOaJuemHj+WuoeaJuSAqLwogICAgY29uZmlybUJhdGNoQXBwcm92YWwoKSB7CiAgICAgIGlmICh0aGlzLmJhdGNoQXBwcm92YWxGb3JtLmFjdGlvbiA9PT0gJ3JlamVjdCcpIHsKICAgICAgICB0aGlzLiRyZWZzWyJiYXRjaEFwcHJvdmFsRm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICAgIGlmICghdmFsaWQpIHJldHVybgogICAgICAgICAgdGhpcy5leGVjdXRlQmF0Y2hBcHByb3ZhbCgpCiAgICAgICAgfSkKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmV4ZWN1dGVCYXRjaEFwcHJvdmFsKCkKICAgICAgfQogICAgfSwKCiAgICAvKiog5omn6KGM5om56YeP5a6h5om5ICovCiAgICBleGVjdXRlQmF0Y2hBcHByb3ZhbCgpIHsKICAgICAgY29uc3QgZGF0YSA9IHsKICAgICAgICBpZHM6IHRoaXMuc2VsZWN0ZWRSZWNvcmRzLm1hcChyZWNvcmQgPT4gcmVjb3JkLmlkKSwKICAgICAgICBhY3Rpb246IHRoaXMuYmF0Y2hBcHByb3ZhbEZvcm0uYWN0aW9uLAogICAgICAgIHJlamVjdFJlYXNvbjogdGhpcy5iYXRjaEFwcHJvdmFsRm9ybS5yZWplY3RSZWFzb24KICAgICAgfQoKICAgICAgYmF0Y2hBcHByb3ZlTGl0aWdhdGlvbkNvc3ROZXcoZGF0YSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcyhg5om56YePJHt0aGlzLmJhdGNoQXBwcm92YWxGb3JtLmFjdGlvbiA9PT0gJ2FwcHJvdmUnID8gJ+mAmui/hycgOiAn5ouS57udJ33lrqHmibnmiJDlip9gKQogICAgICAgIHRoaXMuYmF0Y2hBcHByb3ZhbERpYWxvZ1Zpc2libGUgPSBmYWxzZQogICAgICAgIHRoaXMubG9hZEZlZVJlY29yZHModGhpcy5jdXJyZW50Q2FzZUluZm8ubGl0aWdhdGlvbkNhc2VJZCkKICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+aJuemHj+WuoeaJueWksei0pScpCiAgICAgIH0pCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuQA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/litigation_cost_approval/litigation_cost_approval", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['litigation_cost_approval:litigation_cost_approval:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"litigationCostApprovalList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"案件编号\" align=\"center\" prop=\"caseNumber\" />\n      <el-table-column label=\"案件名称\" align=\"center\" prop=\"caseName\" />\n      <el-table-column label=\"被告姓名\" align=\"center\" prop=\"defendantName\" />\n      <el-table-column label=\"律师费\" align=\"center\" prop=\"lawyerFee\">\n        <template slot-scope=\"scope\">\n          ￥{{ scope.row.lawyerFee || 0 }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"诉讼费\" align=\"center\" prop=\"litigationFee\">\n        <template slot-scope=\"scope\">\n          ￥{{ scope.row.litigationFee || 0 }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"保全费\" align=\"center\" prop=\"preservationFee\">\n        <template slot-scope=\"scope\">\n          ￥{{ scope.row.preservationFee || 0 }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"总费用\" align=\"center\" prop=\"totalMoney\">\n        <template slot-scope=\"scope\">\n          ￥{{ scope.row.totalMoney || 0 }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"审批状态\" align=\"center\" prop=\"approvalStatus\">\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"getStatusTagType(scope.row.approvalStatus)\">\n            {{ getStatusText(scope.row.approvalStatus) }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"申请时间\" align=\"center\" prop=\"applicationTime\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.applicationTime, '{y}-{m}-{d}') }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleViewDetails(scope.row)\"\n            v-hasPermi=\"['litigation_cost_approval:litigation_cost_approval:approve']\"\n          >审批</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 费用详情审批对话框 -->\n    <el-dialog title=\"法诉费用审批\" :visible.sync=\"detailsDialogVisible\" width=\"1200px\" append-to-body>\n      <!-- 案件基本信息 -->\n      <el-descriptions :column=\"3\" border style=\"margin-bottom: 20px\" v-if=\"currentCaseInfo\">\n        <el-descriptions-item label=\"案件编号\">{{ currentCaseInfo.caseNumber }}</el-descriptions-item>\n        <el-descriptions-item label=\"案件名称\">{{ currentCaseInfo.caseName }}</el-descriptions-item>\n        <el-descriptions-item label=\"被告姓名\">{{ currentCaseInfo.defendantName }}</el-descriptions-item>\n        <el-descriptions-item label=\"被告身份证\">{{ currentCaseInfo.defendantIdCard }}</el-descriptions-item>\n        <el-descriptions-item label=\"法院管辖\">{{ currentCaseInfo.courtLocation }}</el-descriptions-item>\n        <el-descriptions-item label=\"案件书记员\">{{ currentCaseInfo.curator }}</el-descriptions-item>\n      </el-descriptions>\n\n      <!-- 费用记录列表 -->\n      <div style=\"margin-bottom: 20px;\">\n        <div style=\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;\">\n          <h4>费用记录</h4>\n          <div>\n            <el-button\n              type=\"success\"\n              size=\"small\"\n              @click=\"handleBatchApprove('approve')\"\n              :disabled=\"selectedRecords.length === 0\"\n            >批量通过</el-button>\n            <el-button\n              type=\"danger\"\n              size=\"small\"\n              @click=\"handleBatchApprove('reject')\"\n              :disabled=\"selectedRecords.length === 0\"\n            >批量拒绝</el-button>\n          </div>\n        </div>\n\n        <el-table\n          :data=\"feeRecords\"\n          @selection-change=\"handleRecordSelectionChange\"\n          v-loading=\"recordsLoading\"\n          border>\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n          <el-table-column label=\"申请时间\" align=\"center\" prop=\"applicationTime\" width=\"150\">\n            <template slot-scope=\"scope\">\n              {{ parseTime(scope.row.applicationTime, '{y}-{m}-{d} {h}:{i}') }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"申请人\" align=\"center\" prop=\"applicationBy\" width=\"100\" />\n          <el-table-column label=\"律师费\" align=\"center\" prop=\"lawyerFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.lawyerFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"诉讼费\" align=\"center\" prop=\"litigationFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.litigationFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"保全费\" align=\"center\" prop=\"preservationFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.preservationFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"布控费\" align=\"center\" prop=\"surveillanceFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.surveillanceFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"公告费\" align=\"center\" prop=\"announcementFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.announcementFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"评估费\" align=\"center\" prop=\"appraisalFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.appraisalFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"执行费\" align=\"center\" prop=\"executionFee\" width=\"80\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.executionFee || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"总费用\" align=\"center\" prop=\"totalMoney\" width=\"100\">\n            <template slot-scope=\"scope\">\n              ￥{{ scope.row.totalMoney || 0 }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"审批状态\" align=\"center\" prop=\"approvalStatus\" width=\"120\">\n            <template slot-scope=\"scope\">\n              <el-tag :type=\"getStatusTagType(scope.row.approvalStatus)\">\n                {{ getStatusText(scope.row.approvalStatus) }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"审批时间\" align=\"center\" prop=\"approveTime\" width=\"150\">\n            <template slot-scope=\"scope\">\n              {{ scope.row.approveTime ? parseTime(scope.row.approveTime, '{y}-{m}-{d} {h}:{i}') : '-' }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"审批人\" align=\"center\" prop=\"approveBy\" width=\"100\" />\n          <el-table-column label=\"拒绝原因\" align=\"center\" prop=\"reasons\" width=\"150\" show-overflow-tooltip />\n          <el-table-column label=\"操作\" align=\"center\" width=\"150\" fixed=\"right\">\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleSingleApprove(scope.row, 'approve')\"\n                v-if=\"canApprove(scope.row.approvalStatus)\"\n              >通过</el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleSingleApprove(scope.row, 'reject')\"\n                v-if=\"canApprove(scope.row.approvalStatus)\"\n              >拒绝</el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"detailsDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 单个审批确认对话框 -->\n    <el-dialog title=\"审批确认\" :visible.sync=\"singleApprovalDialogVisible\" width=\"400px\" append-to-body>\n      <el-form ref=\"singleApprovalForm\" :model=\"singleApprovalForm\" label-width=\"80px\">\n        <el-form-item label=\"审批结果\">\n          <el-tag :type=\"singleApprovalForm.action === 'approve' ? 'success' : 'danger'\">\n            {{ singleApprovalForm.action === 'approve' ? '通过' : '拒绝' }}\n          </el-tag>\n        </el-form-item>\n        <el-form-item\n          label=\"拒绝原因\"\n          prop=\"rejectReason\"\n          v-if=\"singleApprovalForm.action === 'reject'\"\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\n          <el-input\n            v-model=\"singleApprovalForm.rejectReason\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入拒绝原因\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"confirmSingleApproval\">确 定</el-button>\n        <el-button @click=\"singleApprovalDialogVisible = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 批量审批确认对话框 -->\n    <el-dialog title=\"批量审批确认\" :visible.sync=\"batchApprovalDialogVisible\" width=\"400px\" append-to-body>\n      <el-form ref=\"batchApprovalForm\" :model=\"batchApprovalForm\" label-width=\"80px\">\n        <el-form-item label=\"审批结果\">\n          <el-tag :type=\"batchApprovalForm.action === 'approve' ? 'success' : 'danger'\">\n            {{ batchApprovalForm.action === 'approve' ? '批量通过' : '批量拒绝' }}\n          </el-tag>\n        </el-form-item>\n        <el-form-item label=\"选中记录\">\n          <span>{{ selectedRecords.length }} 条记录</span>\n        </el-form-item>\n        <el-form-item\n          label=\"拒绝原因\"\n          prop=\"rejectReason\"\n          v-if=\"batchApprovalForm.action === 'reject'\"\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\n          <el-input\n            v-model=\"batchApprovalForm.rejectReason\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入拒绝原因\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"confirmBatchApproval\">确 定</el-button>\n        <el-button @click=\"batchApprovalDialogVisible = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { \n  listLitigationCostApproval, \n  getLitigationCostSubmissionRecords, \n  singleApproveLitigationCost, \n  batchApproveLitigationCostNew \n} from \"@/api/litigation_cost_approval/litigation_cost_approval\"\n\nexport default {\n  name: \"LitigationCostApproval\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 法诉费用审批表格数据\n      litigationCostApprovalList: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        approvalStatus: null,\n      },\n      // 详情对话框显示状态\n      detailsDialogVisible: false,\n      // 当前案件信息\n      currentCaseInfo: null,\n      // 费用记录列表\n      feeRecords: [],\n      // 费用记录加载状态\n      recordsLoading: false,\n      // 选中的费用记录\n      selectedRecords: [],\n      // 单个审批对话框\n      singleApprovalDialogVisible: false,\n      singleApprovalForm: {\n        id: '',\n        action: '', // 'approve' 或 'reject'\n        rejectReason: ''\n      },\n      // 批量审批对话框\n      batchApprovalDialogVisible: false,\n      batchApprovalForm: {\n        action: '', // 'approve' 或 'reject'\n        rejectReason: ''\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    /** 查询法诉费用审批列表 */\n    getList() {\n      this.loading = true\n      listLitigationCostApproval(this.queryParams).then(response => {\n        this.litigationCostApprovalList = response.rows\n        this.total = response.total\n        this.loading = false\n      })\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\")\n      this.handleQuery()\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('litigation_cost_approval/litigation_cost_approval/export', {\n        ...this.queryParams\n      }, `litigation_cost_approval_${new Date().getTime()}.xlsx`)\n    },\n\n    /** 获取状态文本 */\n    getStatusText(status) {\n      const statusMap = {\n        '0': '未审批',\n        '1': '全部同意',\n        '2': '已拒绝',\n        '3': '法诉主管审批',\n        '4': '总监审批',\n        '5': '财务主管/总监抄送',\n        '6': '总经理/董事长审批'\n      }\n      return statusMap[status] || '未知状态'\n    },\n    /** 获取状态标签类型 */\n    getStatusTagType(status) {\n      const typeMap = {\n        '0': 'info',\n        '1': 'success',\n        '2': 'danger',\n        '3': 'warning',\n        '4': 'warning',\n        '5': 'warning',\n        '6': 'warning'\n      }\n      return typeMap[status] || 'info'\n    },\n    /** 检查是否可以审批 */\n    canApprove(status) {\n      return ['3', '4', '5', '6'].includes(status)\n    },\n\n    /** 查看费用详情和审批 */\n    handleViewDetails(row) {\n      this.currentCaseInfo = row\n      this.detailsDialogVisible = true\n      this.loadFeeRecords(row.litigationCaseId)\n    },\n\n    /** 加载费用记录 */\n    loadFeeRecords(litigationCaseId) {\n      this.recordsLoading = true\n      getLitigationCostSubmissionRecords(litigationCaseId).then(response => {\n        this.feeRecords = response.data || []\n        this.recordsLoading = false\n      }).catch(() => {\n        this.recordsLoading = false\n        this.$modal.msgError('加载费用记录失败')\n      })\n    },\n\n    /** 费用记录选择变化 */\n    handleRecordSelectionChange(selection) {\n      this.selectedRecords = selection\n    },\n\n    /** 单个审批 */\n    handleSingleApprove(record, action) {\n      this.singleApprovalForm.id = record.id\n      this.singleApprovalForm.action = action\n      this.singleApprovalForm.rejectReason = ''\n      this.singleApprovalDialogVisible = true\n    },\n\n    /** 确认单个审批 */\n    confirmSingleApproval() {\n      if (this.singleApprovalForm.action === 'reject') {\n        this.$refs[\"singleApprovalForm\"].validate(valid => {\n          if (!valid) return\n          this.executeSingleApproval()\n        })\n      } else {\n        this.executeSingleApproval()\n      }\n    },\n\n    /** 执行单个审批 */\n    executeSingleApproval() {\n      const data = {\n        id: this.singleApprovalForm.id,\n        action: this.singleApprovalForm.action,\n        rejectReason: this.singleApprovalForm.rejectReason\n      }\n\n      singleApproveLitigationCost(data).then(() => {\n        this.$modal.msgSuccess(`${this.singleApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\n        this.singleApprovalDialogVisible = false\n        this.loadFeeRecords(this.currentCaseInfo.litigationCaseId)\n        this.getList()\n      }).catch(() => {\n        this.$modal.msgError('审批失败')\n      })\n    },\n\n    /** 批量审批 */\n    handleBatchApprove(action) {\n      if (this.selectedRecords.length === 0) {\n        this.$modal.msgError('请选择要审批的记录')\n        return\n      }\n\n      this.batchApprovalForm.action = action\n      this.batchApprovalForm.rejectReason = ''\n      this.batchApprovalDialogVisible = true\n    },\n\n    /** 确认批量审批 */\n    confirmBatchApproval() {\n      if (this.batchApprovalForm.action === 'reject') {\n        this.$refs[\"batchApprovalForm\"].validate(valid => {\n          if (!valid) return\n          this.executeBatchApproval()\n        })\n      } else {\n        this.executeBatchApproval()\n      }\n    },\n\n    /** 执行批量审批 */\n    executeBatchApproval() {\n      const data = {\n        ids: this.selectedRecords.map(record => record.id),\n        action: this.batchApprovalForm.action,\n        rejectReason: this.batchApprovalForm.rejectReason\n      }\n\n      batchApproveLitigationCostNew(data).then(() => {\n        this.$modal.msgSuccess(`批量${this.batchApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\n        this.batchApprovalDialogVisible = false\n        this.loadFeeRecords(this.currentCaseInfo.litigationCaseId)\n        this.getList()\n      }).catch(() => {\n        this.$modal.msgError('批量审批失败')\n      })\n    }\n  }\n}\n</script>\n"]}]}